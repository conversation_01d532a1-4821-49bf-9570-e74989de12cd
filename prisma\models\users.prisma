model User {
    id          String  @id @default(uuid())
    email       String?
    displayName String?

    // User preferences
    avatar   String?
    country  String?
    timezone String?
    phone    String?
    language String  @default("pt-BR")
    theme    String  @default("dark")

    onboarding <PERSON>olean @default(true)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    teams      TeamMembership[]
    workspaces WorkspaceMembership[]

    invitesPerformed WorkspaceInvite[] @relation("Performed")
    invites          WorkspaceInvite[] @relation("Invites")

    /// A single user may have multiple profiles on different channels.
    /// For example a single user on BMS Pulse may have two different GitHub accounts.
    /// It will always have at least one profile for the BMS channel.
    links UserProfileLink[]
}

// A single profile may be linked with multiple BMS Pulse users.
// For example a personal GitHub account may be linked with different BMS users.
// Example: a personal GitHub account and a company GitHub account.
model UserProfileLink {
    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId String

    profile   IntegrationProfile @relation(fields: [profileId], references: [id], onDelete: Cascade)
    profileId String

    workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
    workspaceId String

    @@id(name: "unique", [userId, profileId, workspaceId])
}
