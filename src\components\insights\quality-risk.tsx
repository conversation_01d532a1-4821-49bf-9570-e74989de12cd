'use client';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import LinearProgress from '@mui/material/LinearProgress';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { ArrowUpIcon } from '@phosphor-icons/react/dist/ssr/ArrowUp';
import { BugIcon } from '@phosphor-icons/react/dist/ssr/Bug';
import { FileCodeIcon } from '@phosphor-icons/react/dist/ssr/FileCode';
import type { ApexOptions } from 'apexcharts';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

export function QualityRisk(): React.JSX.Element {
  const t = useTranslations('insights');
  const theme = useTheme();

  // Sample data for rework percentage
  const reworkData = {
    dates: ['Jun 1', 'Jun 2', 'Jun 3', 'Jun 4', 'Jun 5', 'Jun 6', 'Jun 7'],
    percentages: [12, 15, 18, 22, 25, 28, 32],
  };

  // Sample data for bug rate
  const bugRateData = {
    dates: ['Jun 1', 'Jun 2', 'Jun 3', 'Jun 4', 'Jun 5', 'Jun 6', 'Jun 7'],
    incidents: [5, 4, 6, 8, 10, 12, 15],
  };

  // Sample data for test coverage
  const testCoverageData = {
    current: 68,
    target: 80,
    weekly: [60, 62, 65, 67, 68],
  };

  // Chart options for rework chart
  const reworkChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [theme.palette.warning.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    stroke: { curve: 'smooth', width: 3 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: reworkData.dates,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => `${value}%`,
        style: { colors: theme.palette.text.secondary },
      },
    },
    tooltip: {
      y: {
        formatter: (value) => `${value}%`,
      },
    },
  };

  // Chart options for bug rate chart
  const bugRateChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [theme.palette.error.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    stroke: { curve: 'smooth', width: 3 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: bugRateData.dates,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => `${value}`,
        style: { colors: theme.palette.text.secondary },
      },
    },
    tooltip: {
      y: {
        formatter: (value) => `${value} incidents`,
      },
    },
  };

  return (
    <Grid container spacing={3}>
      {/* Rework Question */}
      <Grid size={{ md: 4, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.rework')} subheader={t('metrics.reworkPercentage')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={200}
                options={reworkChartOptions}
                series={[{ name: 'Rework %', data: reworkData.percentages }]}
                type='line'
                width='100%'
              />

              <Stack direction='row' spacing={1} alignItems='center'>
                <ArrowUpIcon color={theme.palette.error.main} fontSize='var(--icon-fontSize-md)' />
                <Typography variant='body2' color='error.main'>
                  Increasing trend (32% this week, 18% last week)
                </Typography>
              </Stack>

              <Typography variant='body2' color='error'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.startRootCause')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* Bug Rate Question */}
      <Grid size={{ md: 4, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.bugRate')} subheader={t('metrics.incidentsPerKLOC')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={200}
                options={bugRateChartOptions}
                series={[{ name: 'Incidents', data: bugRateData.incidents }]}
                type='line'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <BugIcon fontSize='var(--icon-fontSize-lg)' color={theme.palette.error.main} />
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2'>15 incidents per KLOC (200% increase)</Typography>
                  <Typography variant='caption' color='text.secondary'>
                    After merge of feature/user-authentication
                  </Typography>
                </Stack>
              </Stack>

              <Typography variant='body2' color='error'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.triggerQualityGate')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* Test Coverage Question */}
      <Grid size={{ md: 4, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.testCoverage')} subheader={t('metrics.coverageTarget')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Stack direction='row' spacing={2} alignItems='center'>
                <FileCodeIcon fontSize='var(--icon-fontSize-lg)' />
                <Stack sx={{ flex: 1 }}>
                  <Stack direction='row' justifyContent='space-between'>
                    <Typography variant='body2' color='text.secondary'>
                      Current: {testCoverageData.current}%
                    </Typography>
                    <Typography variant='body2' color='text.secondary'>
                      Target: {testCoverageData.target}%
                    </Typography>
                  </Stack>
                  <LinearProgress
                    variant='determinate'
                    value={(testCoverageData.current / testCoverageData.target) * 100}
                    color='primary'
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Stack>
              </Stack>

              <Stack direction='row' spacing={1} alignItems='center'>
                <ArrowUpIcon color={theme.palette.success.main} fontSize='var(--icon-fontSize-md)' />
                <Typography variant='body2' color='success.main'>
                  Weekly improvement: +8% in the last 5 weeks
                </Typography>
              </Stack>

              <Typography variant='body2' color='primary'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.createTestTasks')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}
