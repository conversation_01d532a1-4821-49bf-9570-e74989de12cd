import 'server-only';

import { Permission } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { deleteCookie, getCookie } from 'cookies-next';
import { z as zod } from 'zod';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

// Schema for workspace update
const updateWorkspaceSchema = zod.object({
  name: zod.string().min(1).optional(),
  avatar: zod.string().optional(),
});

/**
 * @swagger
 * /api/workspace/{workspaceId}:
 *   get:
 *     tags:
 *       - Workspace
 *     summary: Get workspace
 *     description: Get a specific workspace by ID
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Workspace details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Workspace'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Workspace not found
 *       500:
 *         description: Server error
 */
export async function GET(_request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Get the workspace if the user is a member
    const workspace = await db.workspace.findFirst({
      where: {
        id: workspaceId,
        members: {
          some: {
            userId: currentUser.uid,
          },
        },
      },
      include: {
        members: {
          where: {
            userId: currentUser.uid,
          },
          include: {
            role: true,
          },
        },
      },
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    return NextResponse.json(workspace);
  } catch (error) {
    console.error('Error getting workspace:', error);
    return NextResponse.json({ error: 'Failed to get workspace' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/workspace/{workspaceId}:
 *   patch:
 *     tags:
 *       - Workspace
 *     summary: Update workspace
 *     description: Update a workspace
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Workspace name
 *               avatar:
 *                 type: string
 *                 description: URL to workspace avatar image
 *     responses:
 *       200:
 *         description: Updated workspace
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Workspace not found
 *       500:
 *         description: Server error
 */
export async function PATCH(request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Check if the user is a member of the workspace with appropriate role
    const membership = await db.workspaceMembership.findFirst({
      where: {
        userId: currentUser.uid,
        workspaceId: workspaceId,
        role: {
          permissions: {
            some: {
              id: Permission.UPDATE_WORKSPACE,
            },
          },
        },
      },
    });

    if (!membership) {
      return NextResponse.json({ error: 'You do not have permission to update this workspace' }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = updateWorkspaceSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ error: 'Invalid request', details: validationResult.error.format() }, { status: 400 });
    }

    // Update the workspace
    const updatedWorkspace = await db.workspace.update({
      where: { id: workspaceId },
      data: validationResult.data,
    });

    return NextResponse.json(updatedWorkspace);
  } catch (error) {
    console.error('Error updating workspace:', error);
    return NextResponse.json({ error: 'Failed to update workspace' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/workspace/{workspaceId}:
 *   delete:
 *     tags:
 *       - Workspace
 *     summary: Delete workspace
 *     description: Delete a workspace
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Success
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Workspace not found
 *       500:
 *         description: Server error
 */
export async function DELETE(request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Check if the user is the owner of the workspace
    const membership = await db.workspaceMembership.findFirst({
      where: {
        userId: currentUser.uid,
        workspaceId: workspaceId,
        role: {
          permissions: {
            some: {
              id: Permission.DELETE_WORKSPACE,
            },
          },
        },
      },
    });

    if (!membership) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    // Delete the workspace (this will cascade delete memberships due to Prisma relations)
    await db.workspace.delete({
      where: { id: workspaceId },
    });

    // Create the response
    const response = NextResponse.json({ success: true });

    // If this was the active workspace, clear the cookie in the response
    const activeWorkspace = await getCookie('selected-workspace', { req: request });
    if (activeWorkspace === workspaceId) {
      deleteCookie('selected-workspace', { req: request, res: response });
    }

    return response;
  } catch (error) {
    console.error('Error deleting workspace:', error);
    return NextResponse.json({ error: 'Failed to delete workspace' }, { status: 500 });
  }
}
