#!/usr/bin/env zx
// Setup script for local development

import 'zx/globals';

$.verbose = true;

// Install libraries and run postinstall scripts
await $`npm install`

// Run migrations to create tables locally
await $`npm run db:push`

// Run seed script to populate tables
await $`npm run db:seed`

// Authenticate with Google Cloud for running applications
await $({ stdio: 'inherit' })`gcloud auth application-default login --no-launch-browser`