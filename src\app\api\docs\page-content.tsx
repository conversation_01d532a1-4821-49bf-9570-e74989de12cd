'use client';

import { useEffect, useRef } from 'react';
import { SwaggerUIBundle } from 'swagger-ui-dist';
import 'swagger-ui-dist/swagger-ui.css';

// TODO: migrate to https://github.com/stoplightio/elements when it is compatible with react 19 ??
// Or maybe use swagger-ui-react
// https://github.com/swagger-api/swagger-ui/issues/10243
// https://www.npmjs.com/package/next-swagger-doc
export default function PageContent({ spec }: { spec: any }) {
  const swaggerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!swaggerRef.current || !spec) return;

    SwaggerUIBundle({
      domNode: swaggerRef.current,
      spec,
    });
  }, [spec]);

  return <div className="w-full" ref={swaggerRef} />;
}