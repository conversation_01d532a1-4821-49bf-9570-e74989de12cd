import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { setCookie } from 'cookies-next';
import { z as zod } from 'zod';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

// Schema for workspace creation
const createWorkspaceSchema = zod.object({
  name: zod.string().min(1, 'Workspace name is required'),
  avatar: zod.string().optional(),
});

/**
 * @swagger
 * /api/workspace:
 *   get:
 *     tags:
 *       - Workspace
 *     summary: Get user workspaces
 *     description: Get all workspaces that the current user has access to
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: List of workspaces
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function GET() {
  try {
    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Get all workspaces where the user has a membership
    const workspaces = await db.workspace.findMany({
      where: {
        members: {
          some: {
            userId: currentUser.uid,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(workspaces);
  } catch (error) {
    console.error('Error fetching workspaces:', error);
    return NextResponse.json({ error: 'Failed to fetch workspaces' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/workspace:
 *   post:
 *     tags:
 *       - Workspace
 *     summary: Create workspace
 *     description: Create a new workspace and make the current user the owner
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Workspace name
 *               avatar:
 *                 type: string
 *                 description: URL to workspace avatar image
 *     responses:
 *       200:
 *         description: Created workspace
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = createWorkspaceSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({ error: 'Invalid request', details: validationResult.error.format() }, { status: 400 });
    }

    const { name, avatar } = validationResult.data;

    // Start a transaction to ensure both workspace and membership are created
    const result = await db.$transaction(async (prismaTransaction) => {
      const ownerRole = await prismaTransaction.role.findFirst({
        where: { id: 'owner' },
      });

      if (!ownerRole) {
        throw new Error('Owner role not found');
      }

      // Create the workspace
      const workspace = await prismaTransaction.workspace.create({
        data: {
          name,
          avatar,
        },
      });

      // Create the workspace membership with the owner role
      const membership = await prismaTransaction.workspaceMembership.create({
        data: {
          userId: currentUser.uid,
          workspaceId: workspace.id,
          roleId: ownerRole.id,
        },
        include: {
          role: true,
        },
      });

      // Set this workspace as the active workspace in cookies
      const response = NextResponse.next();
      setCookie('selected-workspace', workspace.id, {
        req: request,
        res: response,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: 30 * 24 * 60 * 60, // 30 days
      });

      return { workspace, membership };
    });

    return NextResponse.json(result.workspace);
  } catch (error) {
    console.error('Error creating workspace:', error);
    return NextResponse.json({ error: 'Failed to create workspace' }, { status: 500 });
  }
}
