import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import { useTranslations } from 'next-intl';
import * as React from 'react';

export function LoadingIntegrations(): React.JSX.Element {
  const t = useTranslations('settings.integrations');

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', p: 3 }}>
      <CircularProgress />
      <Typography variant='body1' sx={{ mt: 2 }}>
        {t('loader.loadingText' as any)}
      </Typography>
    </Box>
  );
}
