# Git Repository Processor

This processor handles `GitRepositoryEvent` events from GitHub webhooks, ensuring that repositories are properly synchronized in the database for all workspaces that have the relevant GitHub integration installed.

## Features

- Processes repository events (create, delete, archive)
- Updates repository state in database based on event action
- Supports multiple workspaces sharing the same GitHub installation
- Implements idempotent operations for reliable event processing
- Uses transactions to ensure data consistency
- Provides robust error handling to prevent failed events from affecting other processing

## How It Works

1. When a repository event is received, the processor gets the GitHub integration ID from the repository
2. It finds all GitHub integrations with the same installation ID across all workspaces
3. For each matching integration (workspace):
   - Determines repository state and fields to update based on event action
   - Checks if the repository exists for that workspace
   - If it exists, updates it with the latest information
   - If it doesn't exist (and this is not a delete event), creates it
   - For deleted repositories that don't exist, no action is taken

## State Management

Repository state is determined by the event action:
- `Created` → `Active` state
- `Deleted` → `Deleted` state
- `Archived` → `Archived` state

Additionally, the processor updates timestamps and author information:
- `createdAt` / `createdById` for creation events
- `deletedAt` / `deletedById` for deletion events
- `archivedAt` / `archivedById` for archive events
- `updatedAt` always updated to the current time

## Error Handling

The processor implements robust error handling:
- Each workspace's repository processing is isolated from others
- Database errors for one workspace don't affect processing for other workspaces
- All errors are logged but don't prevent the event from being considered processed
- Transactions ensure database consistency

## Testing

Unit tests are provided to verify:
- Repository creation for new repositories
- Repository updates for existing repositories
- Skip creation for deleted repositories
- Error handling
