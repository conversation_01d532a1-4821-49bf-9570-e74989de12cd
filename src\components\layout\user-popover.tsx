import { useRouter } from '@/i18n/navigation';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import ListItemIcon from '@mui/material/ListItemIcon';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import Popover from '@mui/material/Popover';
import Typography from '@mui/material/Typography';
import { SignOutIcon } from '@phosphor-icons/react/dist/ssr/SignOut';
import { UserIcon } from '@phosphor-icons/react/dist/ssr/User';
import { useTranslations } from 'next-intl';
import NextLink from 'next/link';
import * as React from 'react';

import { useAuth } from '@/contexts/firebase-auth-context';
import { useCurrentUser } from '@/contexts/user-context';
import { logger } from '@/lib/logger/default-logger';
import { paths } from '@/paths';

export interface UserPopoverProps {
  anchorEl: Element | null;
  onClose: () => void;
  open: boolean;
}

export function UserPopover({ anchorEl, onClose, open }: UserPopoverProps): React.JSX.Element {
  const { logout } = useAuth();
  const { user } = useCurrentUser();
  const router = useRouter();
  const t = useTranslations('userPopup');

  const handleSignOut = React.useCallback(async (): Promise<void> => {
    try {
      // Clear any cached data
      if (typeof window !== 'undefined') {
        // Clear user data cache
        localStorage.removeItem('user_data_cache');
        localStorage.removeItem('user_data_timestamp');

        // Clear all localStorage items
        localStorage.clear();
        // Clear all sessionStorage items
        sessionStorage.clear();
      }

      // Reset the API service singleton
      // This is now handled in the logout function in firebase-auth-context.tsx

      // Logout from Firebase
      await logout();

      // After logout, redirect to sign in page
      router.push(paths.auth.signIn);
    } catch (err) {
      logger.error('Sign out error', err);
    }
  }, [logout, router]);

  return (
    <Popover
      anchorEl={anchorEl}
      anchorOrigin={{ horizontal: 'left', vertical: 'bottom' }}
      onClose={onClose}
      open={open}
      slotProps={{ paper: { sx: { width: '240px' } } }}
    >
      <Box sx={{ p: '16px 20px ' }}>
        <Typography variant='subtitle1'>{user?.displayName || t('noDisplayName')}</Typography>
        {user?.email && (
          <Typography color='text.secondary' variant='body2'>
            {user.email}
          </Typography>
        )}
      </Box>
      <Divider />
      <MenuList disablePadding sx={{ p: '8px', '& .MuiMenuItem-root': { borderRadius: 1 } }}>
        <MenuItem component={NextLink} href={paths.settings.account} onClick={onClose}>
          <ListItemIcon>
            <UserIcon fontSize='var(--icon-fontSize-md)' />
          </ListItemIcon>
          {t('account')}
        </MenuItem>
        <MenuItem onClick={handleSignOut}>
          <ListItemIcon>
            <SignOutIcon fontSize='var(--icon-fontSize-md)' />
          </ListItemIcon>
          {t('signOut')}
        </MenuItem>
      </MenuList>
    </Popover>
  );
}
