import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import GlobalStyles from '@mui/material/GlobalStyles';
import * as React from 'react';

import { OnboardingGuard } from '@/components/auth/onboarding-guard';
import { WorkspaceGuard } from '@/components/auth/workspace-guard';
import { MainNav } from '@/components/layout/main-nav';
import { SideNav } from '@/components/layout/side-nav';

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps): React.JSX.Element {
  return (
    <OnboardingGuard type='onboarding'>
      <WorkspaceGuard>
        <GlobalStyles
          styles={{
            body: {
              '--MainNav-height': '56px',
              '--MainNav-zIndex': 1000,
              '--SideNav-width': '280px',
              '--SideNav-zIndex': 1100,
              '--MobileNav-width': '320px',
              '--MobileNav-zIndex': 1100,
            },
          }}
        />
        <Box
          sx={{
            bgcolor: 'var(--mui-palette-background-default)',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            minHeight: '100%',
          }}
        >
          <SideNav />
          <Box
            sx={{
              display: 'flex',
              flex: '1 1 auto',
              flexDirection: 'column',
              pl: { lg: 'var(--SideNav-width)' },
            }}
          >
            <MainNav />
            <main>
              <Container maxWidth={false} sx={{ py: '24px' }}>
                {children}
              </Container>
            </main>
          </Box>
        </Box>
      </WorkspaceGuard>
    </OnboardingGuard>
  );
}
