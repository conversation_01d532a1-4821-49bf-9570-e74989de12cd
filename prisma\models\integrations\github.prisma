model GitHubIntegration {
    id String @id @default(uuid())

    updatedAt DateTime @updatedAt

    integration   WorkspaceIntegration @relation(fields: [integrationId], references: [id], onDelete: Cascade)
    integrationId String               @unique

    installationId Int
    accountId      Int
    accountLogin   String
    accountType    GitHubAccountType
}

/// Webhooks received from GitHub.
model GitHubWebhook {
    receivedAt DateTime @default(now())

    /// X-GitHub-Hook-ID
    hookId   String?
    /// X-GitHub-Event
    event    String
    /// X-GitHub-Delivery (GUID)
    delivery String  @id

    /// X-GitHub-Hook-Installation-Target-Type
    hookInstallationTargetType String?
    /// X-GitHub-Hook-Installation-Target-ID
    hookInstallationTargetId   String?

    /// When available, this is the installation ID of the installation that created the webhook (installation.id)
    installationId           Int?
    /// When available, this is the account ID of the installation that created the webhook (installation.account.id)
    installationAccountId    Int?
    /// When available, this is the login of the account that created the webhook (installation.account.login)
    installationAccountLogin String?

    /// If the webhook was processed successfully
    processed Boolean @default(false)

    /// Webhook payload
    data Json

    @@index([installationId])
}

enum GitHubAccountType {
    Enterprise
    Organization
    User
}
