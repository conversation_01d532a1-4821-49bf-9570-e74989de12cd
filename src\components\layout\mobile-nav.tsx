'use client';

import { usePathname } from '@/i18n/navigation';
import Box from '@mui/material/Box';
import Collapse from '@mui/material/Collapse';
import Divider from '@mui/material/Divider';
import Drawer from '@mui/material/Drawer';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { ArrowsLeftRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowsLeftRight';
import { CaretDownIcon } from '@phosphor-icons/react/dist/ssr/CaretDown';
import { CaretRightIcon } from '@phosphor-icons/react/dist/ssr/CaretRight';
import { useTranslations } from 'next-intl';
import NextLink from 'next/link';
import * as React from 'react';

import { DynamicLogo } from '@/components/core/logo';
import { isNavItemActive } from '@/components/layout/is-nav-item-active';
import { useWorkspace } from '@/contexts/workspace-context';
import { paths } from '@/paths';
import type { NavItemConfig } from '@/types/nav';

import { navItems } from './nav-config';
import { navIcons } from './nav-icons';

export interface MobileNavProps {
  onClose?: () => void;
  open?: boolean;
  items?: NavItemConfig[];
}

export function MobileNav({ open, onClose }: MobileNavProps): React.JSX.Element {
  const pathname = usePathname() as string;
  const t = useTranslations('nav');
  const { currentWorkspace } = useWorkspace();

  return (
    <Drawer
      onClose={onClose}
      open={open}
      slotProps={{
        paper: {
          sx: {
            '--MobileNav-background': 'var(--mui-palette-background-default)',
            '--MobileNav-color': 'var(--mui-palette-text-primary)',
            '--NavItem-color': 'var(--mui-palette-text-secondary)',
            '--NavItem-hover-background': 'var(--mui-palette-action-hover)',
            '--NavItem-active-background': 'var(--mui-palette-primary-main)',
            '--NavItem-active-color': 'var(--mui-palette-primary-contrastText)',
            '--NavItem-disabled-color': 'var(--mui-palette-text-disabled)',
            '--NavItem-icon-color': 'var(--mui-palette-text-secondary)',
            '--NavItem-icon-active-color': 'var(--mui-palette-primary-contrastText)',
            '--NavItem-icon-disabled-color': 'var(--mui-palette-text-disabled)',
            backgroundColor: 'var(--mui-palette-background-default) !important',
            backgroundImage: 'none !important',
            color: 'var(--MobileNav-color)',
            display: 'flex',
            flexDirection: 'column',
            maxWidth: '100%',
            scrollbarWidth: 'none',
            width: 'var(--MobileNav-width)',
            zIndex: 'var(--MobileNav-zIndex)',
            '&::-webkit-scrollbar': { display: 'none' },
          },
        },
        backdrop: {
          sx: {
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      }}
    >
      <Stack spacing={3} sx={{ p: 3 }}>
        <Box component={NextLink} href={paths.landing} sx={{ display: 'inline-flex' }}>
          <DynamicLogo height={32} width={122} />
        </Box>
        <Box
          component={NextLink}
          href={paths.workspaceSelection}
          onClick={onClose}
          sx={{
            alignItems: 'center',
            backgroundColor: 'var(--mui-palette-background-level1)',
            border: '1px solid var(--mui-palette-divider)',
            borderRadius: '12px',
            cursor: 'pointer',
            display: 'flex',
            p: '4px 12px',
            textDecoration: 'none',
            color: 'inherit',
            '&:hover': {
              backgroundColor: 'var(--mui-palette-action-hover)',
            },
          }}
        >
          <Box sx={{ flex: '1 1 auto' }}>
            <Typography color='var(--mui-palette-text-secondary)' variant='body2'>
              {t('workspace')}
            </Typography>
            <Typography
              color='inherit'
              variant='subtitle1'
              sx={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {currentWorkspace?.name || 'No workspace'}
            </Typography>
          </Box>
          <ArrowsLeftRightIcon />
        </Box>
      </Stack>
      <Divider sx={{ borderColor: 'var(--mui-palette-divider)' }} />
      <Box component='nav' sx={{ flex: '1 1 auto', p: '12px' }}>
        {renderNavItems({ pathname, items: navItems })}
      </Box>
      <Divider sx={{ borderColor: 'var(--mui-palette-divider)' }} />
    </Drawer>
  );
}

function renderNavItems({ items = [], pathname }: { items?: NavItemConfig[]; pathname: string }): React.JSX.Element {
  const children = items.reduce((acc: React.ReactNode[], curr: NavItemConfig): React.ReactNode[] => {
    const { key, ...item } = curr;

    acc.push(<NavItem key={key} pathname={pathname} {...item} />);

    return acc;
  }, []);

  return (
    <Stack component='ul' spacing={1} sx={{ listStyle: 'none', m: 0, p: 0 }}>
      {children}
    </Stack>
  );
}

interface NavItemProps extends Omit<NavItemConfig, 'key'> {
  pathname: string;
}

function NavItem({ disabled, external, href, icon, items, matcher, pathname, title }: NavItemProps): React.JSX.Element {
  const t = useTranslations('nav');
  const [open, setOpen] = React.useState(false);

  const hasItems = items && items.length > 0;

  // Check if this item or any of its children are active
  const hasActiveChild = items?.some((item) =>
    isNavItemActive({
      disabled: item.disabled,
      external: item.external,
      href: item.href,
      matcher: item.matcher,
      pathname,
    })
  );

  // For parent items, we want to know if this exact item is active
  const exactActive = isNavItemActive({
    disabled,
    external,
    href,
    matcher,
    pathname,
  });

  // For styling purposes, parent items should only be highlighted when they are not parent menus
  // Child items should be highlighted when they are active
  const active = hasItems ? false : exactActive || hasActiveChild;

  // For text styling, we want to know if this item or any of its children are active
  const isBold = exactActive || hasActiveChild;

  // For parent menu color styling when a child is selected
  const isParentWithActiveChild = hasItems && hasActiveChild;

  const Icon = icon ? navIcons[icon] : null;

  // Open the menu by default if it has an active child
  React.useEffect(() => {
    if (hasActiveChild) {
      setOpen(true);
    }
  }, [hasActiveChild]);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  return (
    <li>
      <Box
        {...(href && !hasItems
          ? {
              component: external ? 'a' : NextLink,
              href,
              target: external ? '_blank' : undefined,
              rel: external ? 'noreferrer' : undefined,
            }
          : {
              role: 'button',
              onClick: hasItems ? handleToggle : undefined,
            })}
        sx={{
          alignItems: 'center',
          borderRadius: 1,
          color: 'var(--NavItem-color)',
          cursor: 'pointer',
          display: 'flex',
          flex: '0 0 auto',
          gap: 1,
          p: '6px 16px',
          position: 'relative',
          textDecoration: 'none',
          whiteSpace: 'nowrap',
          ...(disabled && {
            bgcolor: 'var(--NavItem-disabled-background)',
            color: 'var(--NavItem-disabled-color)',
            cursor: 'not-allowed',
          }),
          ...(active && {
            bgcolor: 'var(--NavItem-active-background)',
            color: 'var(--NavItem-active-color)',
          }),
        }}
      >
        <Box
          sx={{
            alignItems: 'center',
            display: 'flex',
            justifyContent: 'center',
            flex: '0 0 auto',
          }}
        >
          {Icon ? (
            <Icon
              fill={
                active
                  ? 'var(--NavItem-icon-active-color)'
                  : isParentWithActiveChild
                    ? 'var(--mui-palette-primary-main)'
                    : 'var(--NavItem-icon-color)'
              }
              fontSize='var(--icon-fontSize-md)'
              weight={active || (hasItems && isBold) ? 'fill' : undefined}
            />
          ) : null}
        </Box>
        <Box sx={{ flex: '1 1 auto' }}>
          <Typography
            component='span'
            sx={{
              color: isParentWithActiveChild ? 'var(--mui-palette-primary-main)' : 'inherit',
              fontSize: '0.875rem',
              fontWeight: isBold ? 700 : 500,
              lineHeight: '28px',
            }}
          >
            {t(title as any)}
          </Typography>
        </Box>
        {hasItems &&
          (open ? (
            <CaretDownIcon
              fill={isBold ? 'var(--mui-palette-primary-main)' : 'var(--NavItem-icon-color)'}
              fontSize='var(--icon-fontSize-md)'
            />
          ) : (
            <CaretRightIcon
              fill={isBold ? 'var(--mui-palette-primary-main)' : 'var(--NavItem-icon-color)'}
              fontSize='var(--icon-fontSize-md)'
            />
          ))}
      </Box>

      {hasItems && (
        <Collapse in={open} timeout='auto' unmountOnExit>
          <Stack
            component='ul'
            spacing={1}
            sx={{
              listStyle: 'none',
              m: 0,
              p: 0,
              pl: 3, // Indent submenu items
              mt: 1,
            }}
          >
            {items.map((item) => (
              <NavItem
                key={item.key}
                disabled={item.disabled}
                external={item.external}
                href={item.href}
                icon={item.icon}
                items={item.items}
                matcher={item.matcher}
                pathname={pathname}
                title={item.title}
              />
            ))}
          </Stack>
        </Collapse>
      )}
    </li>
  );
}
