import { IntegrationChannel, IntegrationStatus, Prisma } from '@prisma/client';
import { CoreApiService } from './core';

/**
 * Integration API service
 * This service provides methods for interacting with the integration API
 */
export class IntegrationApiService {
  private api: CoreApiService;

  /**
   * Create a new IntegrationApiService
   * @param apiService The API service to use
   */
  constructor(apiService: CoreApiService) {
    this.api = apiService;
  }

  /**
   * Create integration state for installation flow
   * @param workspaceId The workspace ID
   * @param channel The integration channel
   * @returns A promise that resolves to the created state
   */
  async createState(workspaceId: string, channel: IntegrationChannel): Promise<{ id: string }> {
    return await this.api.request({
      method: 'POST',
      url: `/workspace/${workspaceId}/integration/state`,
      data: { channel },
    });
  }

  /**
   * Get all integrations for a workspace
   * @param workspaceId The workspace ID
   * @param channel The integration channels to filter results (optional)
   * @returns A promise that resolves to an array of workspace integrations
   */
  async get(
    workspaceId: string,
    channel?: IntegrationChannel[]
  ): Promise<
    Prisma.WorkspaceIntegrationGetPayload<{
      include: { github: true };
    }>[]
  > {
    return await this.api.request({
      method: 'GET',
      url: `/workspace/${workspaceId}/integration`,
      params: { channel },
    });
  }

  /**
   * Uninstall an GitHub installation from a workspace
   * @param workspaceId The workspace ID
   * @param integrationId The integration ID
   * @returns A promise that resolves when the integration is uninstalled
   */
  async uninstall(
    workspaceId: string,
    integrationId: string
  ): Promise<{ otherWorkspaces: { id: string; name: string }[]; executedOnChannel: boolean }> {
    return await this.api.request({
      method: 'DELETE',
      url: `/workspace/${workspaceId}/integration/${integrationId}`,
    });
  }

  /**
   * Suspend an GitHub installation of a workspace
   * @param workspaceId The workspace ID
   * @param integrationId The integration ID
   * @param status The status to set
   * @returns A promise that resolves when the integration is suspended
   */
  async setStatus(
    workspaceId: string,
    integrationId: string,
    status: IntegrationStatus
  ): Promise<{ otherWorkspaces: { id: string; name: string }[]; executedOnChannel: boolean }> {
    return await this.api.request({
      method: 'PATCH',
      url: `/workspace/${workspaceId}/integration/${integrationId}`,
      data: { status },
    });
  }
}

/**
 * Create an integration API service with the given API service
 * @param apiService The API service to use
 * @returns A new integration API service
 */
export function createIntegrationApiService(apiService: CoreApiService): IntegrationApiService {
  return new IntegrationApiService(apiService);
}
