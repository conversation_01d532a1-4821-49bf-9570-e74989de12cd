/**
 * @jest-environment jsdom
 */

import { render, screen } from '@testing-library/react';
import * as React from 'react';

// Mock all external dependencies that would cause issues in the test environment
jest.mock('@/i18n/navigation', () => ({
  Link: ({ children, ...props }: any) => React.createElement('a', props, children),
  useRouter: () => ({ push: jest.fn() }),
}));

jest.mock('@/components/core/logo', () => ({
  DynamicLogo: () => React.createElement('div', { 'data-testid': 'logo' }, 'Logo'),
}));

jest.mock('@/contexts/firebase-auth-context', () => ({
  useAuth: () => ({ user: null, loading: false }),
}));

jest.mock('@/paths', () => ({
  paths: {
    home: '/app/home',
    auth: { signIn: '/auth/sign-in', signUp: '/auth/sign-up' },
  },
}));

// Mock next-intl with simple string returns
jest.mock('next-intl', () => ({
  useTranslations: () => (key: string) => {
    // Return mock translations based on key
    const mockTranslations: Record<string, string> = {
      'hero.title': 'Welcome to GitPulse',
      'hero.subtitle': 'Track your development performance',
      'hero.cta': 'Get Started',
      'features.delivery.title': 'Delivery Analytics',
      'features.delivery.description': 'Track delivery performance',
      'features.git.title': 'Git Integration',
      'features.git.description': 'Connect your repositories',
      'features.team.title': 'Team Collaboration',
      'features.team.description': 'Enhance team productivity',
      'footer.copyright': '© 2024 GitPulse',
    };
    return mockTranslations[key] || key;
  },
}));

describe('LandingPage', () => {
  it('should render without crashing', async () => {
    const { LandingPage } = await import('../landing-page');

    expect(() => {
      render(<LandingPage />);
    }).not.toThrow();
  });
  it('should display hero content', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    // Check that the heading contains both parts (they're in one h1 but split with br tags)
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
    expect(screen.getByText('Track your development performance')).toBeInTheDocument();
  });

  it('should display features section', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    expect(screen.getByText('Delivery Analytics')).toBeInTheDocument();
    expect(screen.getByText('Git Integration')).toBeInTheDocument();
    expect(screen.getByText('Team Collaboration')).toBeInTheDocument();
  });

  it('should display navigation buttons', async () => {
    const { LandingPage } = await import('../landing-page');

    render(<LandingPage />);

    // Check for sign in links (there are multiple)
    expect(screen.getAllByText('hero.signIn')).toHaveLength(2);

    // Check for get started buttons (there are multiple)
    expect(screen.getAllByText('hero.getStartedFree')).toHaveLength(2);
  });
});
