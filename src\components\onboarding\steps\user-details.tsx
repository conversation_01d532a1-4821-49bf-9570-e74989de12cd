'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import Grid from '@mui/material/Grid';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import CountryAutocomplete from '@/components/settings/country-autocomplete';
import TimezoneAutocomplete from '@/components/settings/timezone-autocomplete';
import { useAuth } from '@/contexts/firebase-auth-context';
import { useCurrentUser } from '@/contexts/user-context';

interface UserDetailsStepProps {
  formData: {
    displayName: string;
    email: string;
    phone: string;
    country: string;
    timezone: string;
  };
  onChange: (_field: string, _value: string) => void;
}

type FormValues = {
  displayName: string;
  email?: string;
  phone?: string;
  country: string;
  timezone: string;
};

export const UserDetailsStep = React.forwardRef<{ validate: () => Promise<boolean> }, UserDetailsStepProps>(
  ({ formData, onChange }, ref): React.JSX.Element => {
    const t = useTranslations('onboarding');
    const { user: authUser } = useAuth();
    const { user: dbUser, loading: userLoading } = useCurrentUser();
    const [_loading, setLoading] = React.useState(true);

    // Create schema with translations
    const schema = React.useMemo(() => {
      return zod.object({
        displayName: zod
          .string()
          .max(70, { message: t('userDetails.maxNameSize', { size: '70' }) })
          .min(1, { message: t('userDetails.displayNameRequired') }),
        email: zod.string().email().optional(),
        phone: zod
          .string()
          .optional()
          .refine((value) => !value || matchIsValidTel(value), {
            message: t('userDetails.invalidPhone'),
          }),
        country: zod.string().min(1, { message: t('userDetails.countryRequired') }),
        timezone: zod.string().min(1, { message: t('userDetails.timezoneRequired') }),
      });
    }, [t]);

    // Initialize form with React Hook Form
    const {
      control,
      formState: { errors },
      setValue,
      trigger,
      handleSubmit: _rhfHandleSubmit,
    } = useForm<FormValues>({
      defaultValues: formData,
      resolver: zodResolver(schema),
      mode: 'onBlur',
    });

    // Sync form values with parent component
    const handleFieldChange = React.useCallback(
      (field: keyof FormValues, value: string | null) => {
        setValue(field, value || '');
        onChange(field, value || '');
      },
      [setValue, onChange]
    );

    // Validate the form - this will be called from the parent component
    const validate = React.useCallback(async (): Promise<boolean> => {
      return trigger();
    }, [trigger]);

    // Expose the validate method to the parent component
    React.useImperativeHandle(ref, () => ({ validate }), [validate]);

    // Fetch user email from Firebase auth and DB
    React.useEffect(() => {
      // First try to get email from DB user
      if (dbUser?.email && formData.email !== dbUser.email) {
        setValue('email', dbUser.email);
        onChange('email', dbUser.email);
      }
      // Fallback to Firebase auth email
      else if (authUser?.email && formData.email !== authUser.email) {
        setValue('email', authUser.email);
        onChange('email', authUser.email);
      }

      if (!userLoading) {
        setLoading(false);
      }
    }, [authUser, dbUser, userLoading, formData.email, setValue, onChange]);

    return (
      <Stack spacing={3}>
        <Typography variant='h5' component='h2'>
          {t('userDetails.title')}
        </Typography>
        <Typography variant='body1' color='text.secondary'>
          {t('userDetails.description')}
        </Typography>

        <Grid container spacing={3}>
          <Grid size={{ md: 6, xs: 12 }}>
            <Controller
              control={control}
              name='displayName'
              render={({ field }) => (
                <FormControl fullWidth required error={Boolean(errors.displayName)}>
                  <InputLabel>{t('userDetails.displayName')}</InputLabel>
                  <OutlinedInput
                    {...field}
                    onChange={(e) => handleFieldChange('displayName', e.target.value)}
                    label={t('userDetails.displayName')}
                  />
                  {errors.displayName && <FormHelperText>{errors.displayName.message}</FormHelperText>}
                </FormControl>
              )}
            />
          </Grid>
          <Grid size={{ md: 6, xs: 12 }}>
            <Controller
              control={control}
              name='email'
              render={({ field }) => (
                <FormControl fullWidth required>
                  <InputLabel>{t('userDetails.email')}</InputLabel>
                  <OutlinedInput {...field} label={t('userDetails.email')} type='email' readOnly disabled />
                </FormControl>
              )}
            />
          </Grid>
          <Grid size={{ md: 6, xs: 12 }}>
            <Controller
              name='phone'
              control={control}
              render={({ field: { ref: fieldRef, value, ...fieldProps } }) => (
                <FormControl fullWidth>
                  <MuiTelInput
                    {...fieldProps}
                    onChange={(e) => handleFieldChange('phone', e)}
                    defaultCountry='BR'
                    inputRef={fieldRef}
                    value={value ?? ''}
                    preferredCountries={['BR', 'US']}
                    label={t('userDetails.phone')}
                    helperText={errors.phone ? errors.phone.message : ''}
                    error={Boolean(errors.phone)}
                  />
                </FormControl>
              )}
            />
          </Grid>
          <Grid size={{ md: 6, xs: 12 }}>
            <Controller
              control={control}
              name='country'
              render={({ field }) => (
                <CountryAutocomplete
                  value={field.value}
                  onChange={(value) => handleFieldChange('country', value)}
                  error={Boolean(errors.country)}
                  helperText={errors.country?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ md: 6, xs: 12 }}>
            <Controller
              control={control}
              name='timezone'
              render={({ field }) => (
                <TimezoneAutocomplete
                  value={field.value}
                  onChange={(value) => handleFieldChange('timezone', value)}
                  error={Boolean(errors.timezone)}
                  helperText={errors.timezone?.message}
                />
              )}
            />
          </Grid>
        </Grid>
      </Stack>
    );
  }
);
