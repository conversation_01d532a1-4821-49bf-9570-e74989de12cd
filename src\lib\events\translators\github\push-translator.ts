import { GitCommitEvent, GitPushEvent } from '@/lib/events/types/git';
import { db } from '@/services/db';
import { EmitterWebhookEvent } from '@octokit/webhooks';
import { Committer, Repository, User } from '@octokit/webhooks-types';
import { IntegrationChannel } from '@prisma/client';
import { logger } from '../../../logger/default-logger';
import { eventTranslatorRegistry } from '../registry';
import { convertCommiterToUser, convertToIntegrationProfile } from './author-converter';
import { getRepositoryFromEvent } from './repository-translator';

/**
 * Register GitHub push event translator
 */
eventTranslatorRegistry.registry(
  IntegrationChannel.GitHub,
  GitPushEvent,
  async (event: EmitterWebhookEvent<'push'>) => {
    const now = new Date();

    const { repository, sender, commits, pusher, ref } = event.payload;

    const installationId = event.payload.installation?.id;

    if (!installationId) {
      logger.warn('Invalid installation ID, ignoring event', { eventId: event.id });
      return null;
    }

    if (!repository.created_at) {
      logger.warn('Repository created_at field is null, ignoring event', { eventId: event.id });
      return null;
    }

    if (!commits?.length) {
      // TODO: deal with pushing new branch
      logger.warn('No commits in push event, ignoring', { eventId: event.id });
      return null;
    }

    if (!sender) {
      logger.warn('Sender information is missing in push event, ignoring', { eventId: event.id });
      return null;
    }

    const integrations = await db.workspaceIntegration.findMany({
      where: {
        integrationIdOnChannel: installationId.toString(),
        channel: IntegrationChannel.GitHub,
      },
      include: {
        github: true,
      },
    });

    const events: GitPushEvent[] = [];

    for (const integration of integrations) {
      const workspaceId = integration.workspaceId;
      const integrationId = integration.id;

      // Extract branch name from ref (refs/heads/main -> main)
      const branch = ref.replace('refs/heads/', '');

      const repositoryData = getRepositoryFromEvent({
        repository: repository as Partial<Repository>,
        now,
        integrationId,
        workspaceId,
      });

      // Convert GitHub commits to our internal format
      const gitCommits: Partial<GitCommitEvent>[] = commits
        .map((commit) => {
          const author = convertCommiterToUser(commit.author as Committer);
          const committer = convertCommiterToUser(commit.committer as Committer);

          if (!author) {
            logger.warn('Commit author is missing, skipping commit', {
              commitId: commit.id,
              eventId: event.id,
            });

            return null;
          }

          if (!committer) {
            logger.warn('Commit committer is missing, skipping commit', {
              commitId: commit.id,
              eventId: event.id,
            });

            return null;
          }

          const commitData: Partial<GitCommitEvent> = new GitCommitEvent({
            id: commit.id,
            integrationId: integrationId,
            workspaceId: workspaceId,
            timestamp: new Date(commit.timestamp),
            repository: repositoryData,
            channel: IntegrationChannel.GitHub,
            author: convertToIntegrationProfile({
              user: author,
              workspaceId,
            }),
            committer: convertToIntegrationProfile({
              user: committer,
              workspaceId,
            }),
            commit: {
              sha: commit.id,
              url: commit.url,
              branch: branch,
              message: commit.message,
              committedAt: new Date(commit.timestamp),
              modifiedFiles: commit.modified || [],
              addedFiles: commit.added || [],
              deletedFiles: commit.removed || [],
            },
            rawPayload: commit,
          });

          return commitData;
        })
        .filter((commit) => {
          return commit !== null;
        });

      events.push(
        new GitPushEvent({
          id: event.id,
          channel: IntegrationChannel.GitHub,
          integrationId: integrationId,
          workspaceId: workspaceId,
          timestamp: now,
          repository: repositoryData,
          author: convertToIntegrationProfile({
            user: sender as Partial<User>,
            additionalData: convertCommiterToUser(pusher as Committer),
            workspaceId,
          }),
          branch,
          commits: gitCommits,
          forcePush: event.payload.forced,
          rawPayload: event,
        })
      );
    }

    return events;
  }
);
