import {
  Integration,
  IntegrationHandler,
  IntegrationInstallProps,
  IntegrationUninstallProps,
} from '@/services/integrations/integration';
import { IntegrationChannel } from '@prisma/client';

// Jira integration data
const jiraIntegration: Integration = {
  id: IntegrationChannel.Jira,
  title: 'Jira',
  status: 'coming-soon',
  capabilities: ['issueTracking', 'sprintPerformance', 'backlogAnalysis', 'projectTimeline', 'teamWorkload'],
};

// Jira integration handler
export const jiraHandler: IntegrationHandler = {
  // Return the integration data
  getIntegration: () => {
    return jiraIntegration;
  },

  // Handle installation (not implemented for coming soon)
  onInstallAction: async (_props: IntegrationInstallProps) => {
    console.log('Installing Jira integration');
    // Not implemented for coming soon
    return false;
  },

  // Handle uninstallation (not implemented for coming soon)
  onUninstallAction: async (_props: IntegrationUninstallProps) => {
    console.log('Uninstalling Jira integration');
    // Not implemented for coming soon
    return false;
  },

  // This integration doesn't have a custom install flow
  hasCustomInstallFlow: () => false,
};
