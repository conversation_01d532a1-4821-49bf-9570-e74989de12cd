This will allow your web application to access resources stored in Google Cloud Storage from a custom origin.

https://cloud.google.com/storage/docs/using-cors#command-line
https://firebase.google.com/docs/storage/web/download-files#cors_configuration

```bash
gsutil cors set development.json gs://pulse-storage-development
gsutil cors set production.json gs://build-manage-scale.firebasestorage.app

# Then to validate

gsutil cors get gs://pulse-storage-development
gsutil cors get gs://build-manage-scale.firebasestorage.app
```

Alternatively, you can use the gcloud CLI to set CORS configuration for the storage:

```bash
gcloud storage buckets update gs://pulse-storage-development --cors-file=development.json
gcloud storage buckets update gs://build-manage-scale.firebasestorage.app --cors-file=production.json
```
