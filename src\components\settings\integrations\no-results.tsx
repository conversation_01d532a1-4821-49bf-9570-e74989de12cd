'use client';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { MagnifyingGlassIcon } from '@phosphor-icons/react/dist/ssr/MagnifyingGlass';
import { useTranslations } from 'next-intl';
import * as React from 'react';

interface NoResultsProps {
  onClearSearchAction: () => void;
}

export function NoResults({ onClearSearchAction }: NoResultsProps): React.JSX.Element {
  const t = useTranslations('settings.integrations');

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 8,
        px: 3,
      }}
    >
      {' '}
      <MagnifyingGlassIcon
        size={64}
        color='currentColor'
        style={{ color: 'var(--mui-palette-text-secondary)', marginBottom: 16 }}
      />
      <Stack spacing={2} sx={{ textAlign: 'center', maxWidth: 400 }}>
        <Typography variant='h6' color='text.primary'>
          {t('noResults.title' as any)}
        </Typography>
        <Typography variant='body2' color='text.secondary'>
          {t('noResults.description' as any)}
        </Typography>{' '}
        <Button variant='outlined' onClick={onClearSearchAction} sx={{ mt: 2 }}>
          {t('noResults.clearSearch' as any)}
        </Button>
      </Stack>
    </Box>
  );
}
