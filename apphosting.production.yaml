env:
  - variable: NODE_ENV
    value: production

  # Database configuration
  - variable: DATABASE_URL
    secret: production-database-url
  - variable: DATABASE_DIRECT_URL
    secret: production-database-url

  # GitHub App configuration
  - variable: GITHUB_APP_ID
    secret: production-github-app-id
  - variable: NEXT_PUBLIC_GITHUB_APP_NAME
    secret: production-github-app-name
  - variable: GITHUB_APP_PRIVATE_KEY
    secret: production-github-app-private-key
  - variable: GITHUB_WEBHOOK_SECRET
    secret: production-github-webhook-secret
  - variable: GITHUB_CLIENT_ID
    secret: production-github-client-id
  - variable: GITHUB_CLIENT_SECRET
    secret: production-github-client-secret
