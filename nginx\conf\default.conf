server {
    listen 80;
    server_name bms-tech-pulse.com www.bms-tech-pulse.com;

    # Redirect to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name bms-tech-pulse.com www.bms-tech-pulse.com;

    ssl_certificate /etc/nginx/ssl/bms-tech-pulse.crt;
    ssl_certificate_key /etc/nginx/ssl/bms-tech-pulse.key;

    # Application
    location / {
        proxy_pass http://application:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}