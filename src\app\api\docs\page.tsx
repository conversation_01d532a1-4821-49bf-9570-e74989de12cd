import { config } from '@/config';
import { createSwaggerSpec } from 'next-swagger-doc';
import PageContent from './page-content';

async function getApiDocs() {
    const spec = createSwaggerSpec({
        apiFolder: 'src/app/api',
        schemaFolders: ['prisma/openapi'],
        definition: {
            openapi: '3.0.0',
            info: {
                title: config.site.name,
                description: config.site.description,
                version: '1.0',
            },
            components: {
                securitySchemes: {
                    BearerAuth: {
                        type: 'http',
                        scheme: 'bearer',
                        bearerFormat: 'JWT',
                    },
                },
            },
            tags: [
                {
                    name: 'User',
                    description: 'User management operations',
                },
            ],
            security: [],
        },
    });
    return spec;
}


export default async function Page() {
    const spec = await getApiDocs();

    return <html><body><PageContent spec={spec} /></body></html>;
}