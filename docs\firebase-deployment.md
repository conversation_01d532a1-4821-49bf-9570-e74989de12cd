# Firebase App Hosting Deployment Guide

This guide explains how to deploy the BMS Tech Pulse frontend to Firebase App Hosting.

## Prerequisites

1. Firebase CLI installed:
   ```
   npm install -g firebase-tools
   ```

2. Firebase account and project access
3. Node.js 22+ installed

## Configuration Files

The following configuration files have been set up for deployment:

1. **firebase.json** - Main Firebase configuration file
2. **.firebaserc** - Links your local project to your Firebase project
3. **apphosting.yaml** - App Hosting configuration with minimal settings to save costs
4. **.env.production** - Production environment variables
5. **src/lib/firebase-config.ts** - Helper to handle Firebase configuration in different environments

## Environment Variables

Firebase App Hosting automatically provides the following environment variables during build and runtime:

- `FIREBASE_CONFIG` - Contains basic Firebase project information
- `FIREBASE_WEBAPP_CONFIG` - Contains all Firebase web app configuration including API keys

Our `firebase-config.ts` helper automatically uses these environment variables when deployed to Firebase App Hosting.

## Deployment Steps

1. **Login to Firebase**:
   ```
   firebase login
   ```

2. **Update API URL**:
   Edit `apphosting.yaml` and update the `NEXT_PUBLIC_API_URL` to point to your production API.

3. **Build and Deploy**:
   ```
   firebase deploy
   ```

   This will:
   - Build your Next.js application
   - Deploy it to Firebase App Hosting
   - Configure the necessary environment variables

## Troubleshooting

If you encounter issues with Firebase authentication during deployment:

1. Make sure your Firebase project has the Web SDK configuration set up correctly
2. Verify that the Firebase project ID in `.firebaserc` matches your actual Firebase project
3. Check that the Firebase App Hosting service is enabled in your Firebase project

## Cost Optimization

The `apphosting.yaml` file has been configured to minimize costs:

- Uses the smallest instance class (F1)
- Scales to zero instances when not in use (min_instances: 0)
- Sets idle instances to 0 to avoid costs when there's no traffic
- Configures longer pending latency to reduce instance startups
- Sets up proper static file handling with long cache expiration

## Additional Resources

- [Firebase App Hosting Documentation](https://firebase.google.com/docs/app-hosting)
- [Next.js Deployment Documentation](https://nextjs.org/docs/deployment)
- [Firebase CLI Reference](https://firebase.google.com/docs/cli)
