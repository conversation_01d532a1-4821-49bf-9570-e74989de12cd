# Architecture

## Three-Layer Processing Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Provider       │     │  Generic        │     │  Database        │
│  Specific       │──→──│  Event          │──→──│  Operations      │
│  Webhooks       │     │  Processors     │     │  Layer           │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### Layer 1: Provider-Specific Webhooks
- Receive raw webhooks from providers (GitHub, GitLab, etc.)
- Validate signatures and authentication
- Translate provider-specific events to standardized internal models using Prisma types
- Handle provider-specific API interactions (e.g., using Octokit for GitHub)
- Store raw payloads for debugging and audit purposes

### Layer 2: Generic Event Processors
- Process standardized events in a provider-agnostic way
- Apply business logic consistently across all providers
- Orchestrate database operations
- Implement retry mechanisms for failed operations
- Support asynchronous processing via message queues for high-volume webhooks

### Layer 3: Database Operations
- Handle all database interactions using Prisma client
- Implement repository pattern for data access
- Ensure data integrity and constraints
- Use transactions when multiple operations need to be atomic
- Leverage Prisma's generated types for type safety across layers

## Directory Structure

Using GitHub as an example provider, the directory structure is organized to separate concerns and maintain clarity in the codebase. The following structure illustrates how the components are organized:

```
src/
├── app/
│   └── api/
│       └── integration/
│           └── webhook/
│               └── github/
│                   ├── route.ts           # Webhook endpoint handler
│                   └── handler.ts         # GitHub event handlers
├── lib/
│   └── events/
│       ├── types/                         # Standardized event types
│       ├── translators/
│       │   ├── registry.ts                # Translator registry
│       │   └── git/
│       │       └── github-translator.ts   # GitHub to internal event translator
│       └── processors/
│           ├── registry.ts                # Generic processor registry
│           └── git/
│               └── git-repository-processor.ts  # Generic git repository event processor
└── services/
    └── integrations/
        └── registry.ts                    # Integrations registry
```

## Code Flow

### Event Translation Layer

The event translation layer converts provider-specific payloads into standardized internal event objects:

1. **Translator Registry**: A centralized registry that maps integration channels and event types to translator functions.
   - Each translator is registered for a specific channel (e.g., GitHub) and target event type.
   - Uses TypeScript generics to ensure type safety across the translation process.

2. **Provider-Specific Translators**: Specialized translator functions that handle the conversion logic.
   - Example: GitHub repository events are translated to our internal `GitRepositoryEvent` format.
   - Handle provider-specific data structures and map them to our standardized models.
   - Filter irrelevant events by returning `null` for events we don't need to process.

```typescript
// Example of translator registration
eventTranslatorRegistry.registry(
  IntegrationChannel.GitHub,
  GitRepositoryEvent,
  (event: EmitterWebhookEvent<'repository'>) => {
    // Translation logic
    return new GitRepositoryEvent({ /* mapped properties */ });
  }
);
```

### Generic Event Processing

The event processing layer handles the standardized events in a provider-agnostic way:

1. **Webhook Routes**: The connection between webhooks and processors.
   - Webhook handlers use the translator registry to convert incoming payloads.
   - The resulting standardized events are then passed to the processor registry.

2. **Translator Registry**: Maintains a mapping between event types and their corresponding translators.
   - Translators are registered by a integration channel and the event type (constructor)
   - Type-safe translation ensures each event is handled by its designated translator.

3. **Processor Registry**: Maintains a mapping between event types and their corresponding processors.
   - Processors are registered by event type (constructor).
   - Type-safe processing ensures each event is handled by its designated processor.

4. **Event Processors**: Handle business logic for each event type.
   - Focus on what needs to happen, not how the event was received.
   - Implement consistent behavior regardless of the source integration.

```typescript
// Example of processor registration
eventProcessorRegistry.register(GitRepositoryEvent, async (event) => {
  // Process the event (database operations, notifications, etc.)
  logger.info(`Processing ${event.id} event for repository ${event.repository.name}...`);
});
```

## Benefits of This Architecture

1. **Decoupling**: Each layer operates independently, making it easier to modify, test, and reason about.

2. **Extensibility**: Adding support for new providers only requires new translators; the processing logic remains the same.

3. **Type Safety**: TypeScript generics ensure type consistency throughout the pipeline.

4. **Testability**: Each component can be tested in isolation with appropriate mock data.

5. **Maintainability**: Clear separation of concerns makes the codebase easier to maintain.

6. **Consistency**: Business logic is applied uniformly across all integrations.

7. **Resilience**: Centralized error handling and retry mechanisms improve system robustness.