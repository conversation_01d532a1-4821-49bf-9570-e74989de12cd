model Workspace {
  /// The workspace auto-generated id
  id String @id @default(uuid())

  /// The workspace name.
  /// Multiple workspaces may have the same name.
  name String?

  /// The workspace avatar picture
  avatar String?

  /// Date when the workspace have been created
  createdAt DateTime @default(now())

  /// Last modification of the workspace
  updatedAt DateTime              @updatedAt
  roles     Role[]
  teams     Team[]
  invites   WorkspaceInvite[]
  members   WorkspaceMembership[]

  integrationStates IntegrationState[]
  integrations      WorkspaceIntegration[]

  integrationProfiles     IntegrationProfile[] @ignore
  integrationProfileLinks UserProfileLink[]    @ignore
  gitRepositories         GitRepository[]      @ignore
  tasks                   Task[]               @ignore
}

model WorkspaceMembership {
  /// The user id
  userId      String
  workspaceId String
  roleId      String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  role        Role      @relation(fields: [roleId], references: [id])
  user        User      @relation(fields: [userId], references: [id])
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@unique([userId, workspaceId])
}

model WorkspaceInvite {
  id            String       @id @default(uuid())
  email         String?
  token         String?
  status        InviteStatus @default(PENDING)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  workspaceId   String?
  invitedBy     String?
  userId        String?
  invitedByUser User?        @relation("Performed", fields: [invitedBy], references: [id])
  user          User?        @relation("Invites", fields: [userId], references: [id])
  workspace     Workspace?   @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
}

enum InviteStatus {
  PENDING
  ACCEPTED
  REJECTED
}
