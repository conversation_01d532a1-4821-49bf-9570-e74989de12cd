---
mode: 'agent'
tools: ['codebase', 'get-library-docs', 'resolve-library-id']
description: 'Update JSDoc Swagger Definitions for routes'
---

## Update JSDoc Swagger Definitions

Please analyze the current file and update all JSDoc comments with complete and accurate @swagger definitions that match the actual implementation. Follow these specific requirements:

To get more context of the syntax, get `next-swagger-doc` or `swagger-ui-dist` documentation using #tool:get-library-docs if needed.

### Requirements:

1. **Match Implementation to Documentation**:

   - Ensure parameter types match actual function parameters
   - Ensure request body definitions match actual request processing
   - Verify path parameters are correctly documented
   - If properties are defined using individual fields of a model, keep same format. Prisma models can be filtered by the code, and you must infer which fields are really used to only include what is necessary in the properties.

2. **Complete Status Code Documentation**:

   - Document ALL possible response status codes (200, 201, 400, 401, 403, 404, 500, etc.) but only if they are actually returned by the endpoint
   - Include appropriate description for each status code
   - Ensure response schemas match what the code actually returns

3. **Use Schema References**:

   - For all Prisma models, use `$ref: '#/components/schemas/ModelName'` instead of inline schemas
   - Example: `$ref: '#/components/schemas/User'` for User model

4. **Verify Request Bodies**:

   - Ensure request body schemas match the actual validation logic
   - Document required vs optional properties correctly

Please provide the complete updated JSDoc for each endpoint in the file, maintaining any existing documentation that's already correct.
