---
applyTo: "**/*.tsx"
---

# Frontend Instructions

- In the react components, use `@phosphor-icons` for icons, and `@mui/material` for UI components
- For the `@phosphor-icons`, always use imports with the component ending on `Icon` since the components without `Icon` suffix are deprecated. Do not rename the component on imports, use the original name.
  - DO NOT USE `@mui/icons-material` for icons.
- If needed, use the `context7` tool to get latest information about the used libraries
- Avoid installing a library in the application, always ask it first before installing
- Always localize the texts in the frontend components. Message files are in the `messages` root folder (`en-US.json`, `es.json` and `pt-BR.json` files)
- Prefer `Snackbar` over `Alert` for errors/notifications after user actions. Anchor snackbars on the bottom.
- Use the App Router and server components, make sure to always separate client and server components when needed.
- Minimize the use of `useEffect` and prefer using server components where possible. But for user actions, keep api calls on the client-side.
- Always use `mui/material-ui` components as baseline. Only create custom components if they will be reused for more than a single place.
  - If needed search for recent documentation of components on the #tool:context7 using the libraryName `mui/material-ui`
- All pages must be compatible with both light and dark themes.
- Prefer using Skeleton components simulating the component structure than circular loaders

## Internationalization

- Use `next-intl` for all text content and make sure to always translate all text on all pages and components.
- The translation keys are defined on the following files:
  - Portuguese (Brasil): #file:../../messages/pt-BR.json
  - English: #file:../../messages/en-US.json
  - Spanish: #file:../../messages/es.json
- ALWAYS keep the translation keys in sync between all messages files and the hierarchy similar to the folder structure.
- Use the `useTranslations` hook to access translations in non-async components and `getTranslations` in async components.
- NEVER add hardcoded text in the frontend code, always use the translation keys.
