import 'server-only';

import { logger } from '@/lib/logger/default-logger';
import { asIntegrationChannel } from '@/lib/models/integration';
import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import { Permission } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Handles the creation of an integration state for a specific workspace and channel.
 *
 * @swagger
 * /api/workspace/{workspaceId}/integration/state:
 *   post:
 *     security:
 *       - BearerAuth: []
 *     tags:
 *       - Integration
 *       - Workspace
 *     summary: Create or retrieve integration state
 *     description: Creates a new integration state for the specified workspace and channel, or retrieves the existing state if it already exists.
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The workspace ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               channel:
 *                 $ref: '#/components/schemas/IntegrationChannel'
 *                 description: The integration channel (e.g., GitHub, Slack)
 *     responses:
 *       200:
 *         description: Integration state created or found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/IntegrationState'
 *       400:
 *         description: Invalid integration channel
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized - user is not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       403:
 *         description: Forbidden - user does not have permission to manage integrations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Error creating integration state
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
export async function POST(
  request: NextRequest,
  props: { params: Promise<{ workspaceId: string }> }
): Promise<NextResponse> {
  const params = await props.params;
  try {
    const { workspaceId } = params;
    const body = await request.json();
    const { channel } = body;

    const integrationChannel = asIntegrationChannel(channel);

    if (!integrationChannel) {
      return NextResponse.json({ message: 'Invalid integration channel' }, { status: 400 });
    }

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const currentUser = authResult.currentUser;

    const response = db.$transaction(async (tx) => {
      // Check if the user can manage integrations
      const havePermission = await db.workspaceMembership
        .findFirst({
          where: {
            userId: currentUser.uid,
            workspaceId: workspaceId,
            role: {
              permissions: {
                some: {
                  id: Permission.MANAGE_INTEGRATIONS,
                },
              },
            },
          },
        })
        .then((r) => Boolean(r));

      if (!havePermission) {
        return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
      }

      const state = await tx.integrationState.findFirst({
        where: {
          workspaceId: workspaceId,
        },
      });

      if (state) {
        return NextResponse.json(state, { status: 200 });
      }

      return NextResponse.json(
        await tx.integrationState.create({
          data: {
            workspaceId: workspaceId,
            channel: integrationChannel,
          },
        }),
        { status: 200 }
      );
    });

    return response;
  } catch (error) {
    logger.error('Error creating github integration state', error);

    return NextResponse.json({ message: 'Error creating github integration state' }, { status: 500 });
  }
}
