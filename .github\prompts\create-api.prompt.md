---
mode: 'agent'
tools: ['codebase', 'usages', 'extensions', 'get-library-docs', 'resolve-library-id', 'findTestFiles', 'searchResults', 'problems']
description: 'Generate a new API route'
---

Your goal is to create a new route in the API.

You must ask the user if not provided with the initial prompt:
- `method` (GET, POST, PUT, DELETE, etc.)
- the path template to define the route path
- the response type to define the response type. When defining response type, prefer using prisma types from the #file:../../prisma/models folder

API routes are defined on #file:../../src/app/api folder and follows nextjs semantic for api routes.

You must create or edit a `route.ts` file to create the route with the user specifications.

Requirements for the api:

- Keep `jsdoc` up to date with the `@swagger` definition based on the implementation
- Always use NextRequest and NextResponse
- Always catch errors and return a NextResponse with the correct status code and message
- Do not expose internal errors messages and stacktraces to the api response
- You must keep the hook #file:../../src/hooks/use-api-services.ts and the mocks in the #file:../../src/hooks/__mocks__/use-api-services.ts up to date
- You must create/edit the frontend client component in the #file:../../src/services/api to access the api you generate, you must return the same type used in the response of the API
- If you need to get documentation of any technology, use the #tool:context7 to fetch extra information

You must follow instructions of the #file:../instructions/api-routes.instructions.md file when creating a new route.