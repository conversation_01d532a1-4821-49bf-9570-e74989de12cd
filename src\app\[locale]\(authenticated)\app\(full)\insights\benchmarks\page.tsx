import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { BenchmarksComparison } from '@/components/insights/benchmarks-comparison';
import { config } from '@/config';

export const metadata = {
  title: `Benchmarks & Comparison | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('insights');

  return (
    <Stack spacing={3}>
      <Typography variant='h4'>{t('categories.benchmarksComparison')}</Typography>
      <Typography color='text.secondary' variant='body1'>
        Comparing performance against industry standards and internal team benchmarks.
      </Typography>

      <BenchmarksComparison />
    </Stack>
  );
}
