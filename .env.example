# -- Client Environment Variables --

## API URL
NEXT_PUBLIC_API_URL=/api

## Firebase
## Note: these environment variables are not required for Firebase Hosting App, only for local development
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=

# -- Server Environment Variables --

## Firebase Admin SDK
## Note: these environment variables are not required for Firebase Hosting App, only for local development
FIREBASE_ADMIN_PROJECT_ID=
FIREBASE_ADMIN_PRIVATE_KEY=
FIREBASE_ADMIN_CLIENT_EMAIL=
FIREBASE_ADMIN_STORAGE_BUCKET=

## GitHub App Configuration
GITHUB_APP_ID=
NEXT_PUBLIC_GITHUB_APP_NAME=
GITHUB_APP_PRIVATE_KEY=
GITHUB_WEBHOOK_SECRET=
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=

## PostgreSQL Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/bms-tech-pulse
DATABASE_DIRECT_URL=postgresql://postgres:postgres@localhost:5432/bms-tech-pulse