'use client';

import { CircularProgress, useColorScheme } from '@mui/material';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Skeleton from '@mui/material/Skeleton';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Typography from '@mui/material/Typography';
import { ArrowSquareOutIcon, PlugsConnectedIcon, PlugsIcon } from '@phosphor-icons/react';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Link } from '@/i18n/navigation';

import { CombinedIntegration } from '@/lib/models/integration';

import { paths } from '@/paths';
import { ViewMode } from './view-toggle';

export interface IntegrationsListProps {
  integrations: CombinedIntegration[];
  viewMode: ViewMode;
  isLoading?: boolean;
  onInstallAction?: (_integration: CombinedIntegration) => Promise<boolean>;
  onUninstallAction?: (_integration: CombinedIntegration) => Promise<boolean>;
  skeletonCount?: number;
}

export function IntegrationsList({
  integrations,
  viewMode,
  isLoading = false,
  onInstallAction,
  skeletonCount = 6,
}: IntegrationsListProps): React.JSX.Element {
  const t = useTranslations('settings.integrations');
  const { colorScheme } = useColorScheme();
  const [installingIds, setInstallingIds] = React.useState<string[]>([]);

  const handleInstall = async (integration: CombinedIntegration) => {
    setInstallingIds((prev) => [...prev, integration.id]);
    const success = await onInstallAction?.(integration);

    if (!success) {
      setInstallingIds((prev) => prev.filter((id) => id !== integration.id));
    }
  };

  // Render skeleton placeholders for card view when loading
  const renderCardSkeleton = () => (
    <Grid container spacing={3}>
      {Array.from({ length: skeletonCount }).map((_, index) => (
        <Grid key={index} size={{ lg: 4, md: 6, xs: 12 }}>
          <Card sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
            <CardContent sx={{ flex: '1 1 auto' }}>
              <Stack spacing={2}>
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <Skeleton variant='rectangular' width={80} height={80} />
                </Box>
                <Stack spacing={1}>
                  <Skeleton variant='text' width='60%' />
                  <Skeleton variant='text' width='80%' />
                </Stack>
              </Stack>
            </CardContent>
            <Divider />
            <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
              <Skeleton variant='rectangular' width={80} height={32} />
              <Skeleton variant='rectangular' width={80} height={32} />
            </CardActions>
          </Card>
        </Grid>
      ))}
    </Grid>
  );

  // Render skeleton placeholders for table view when loading
  const renderTableSkeleton = () => (
    <Card>
      <Box sx={{ overflowX: 'auto' }}>
        <Table sx={{ minWidth: '800px' }}>
          <TableHead>
            <TableRow>
              <TableCell>
                <Skeleton variant='text' width='80px' />
              </TableCell>
              <TableCell>
                <Skeleton variant='text' width='120px' />
              </TableCell>
              <TableCell>
                <Skeleton variant='text' width='100px' />
              </TableCell>
              <TableCell align='right'>
                <Skeleton variant='text' width='80px' />
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Array.from({ length: skeletonCount }).map((_, index) => (
              <TableRow hover key={index}>
                <TableCell>
                  <Stack direction='row' spacing={2} alignItems='center'>
                    <Skeleton variant='rectangular' width={40} height={40} />
                    <Skeleton variant='text' width={100} />
                  </Stack>
                </TableCell>
                <TableCell>
                  <Skeleton variant='text' width='80%' />
                </TableCell>
                <TableCell>
                  <Skeleton variant='text' width='60%' />
                </TableCell>
                <TableCell align='right'>
                  <Skeleton variant='rectangular' width={120} height={32} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Box>
    </Card>
  );

  // Render a single integration card
  const renderCard = (integration: CombinedIntegration) => {
    const isComingSoon = integration.status === 'coming-soon';
    // Use the 'installed' property from CombinedIntegration
    const isInstalled = integration.installed;

    const useDarkLogo = colorScheme === 'dark' && integration.darkLogo;

    const logoPath = `/assets/logos/${integration.id}${useDarkLogo ? '-dark' : ''}.svg`;

    return (
      <Card sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <CardContent sx={{ flex: '1 1 auto' }}>
          <Stack spacing={2}>
            <Box sx={{ display: 'flex', justifyContent: 'center', position: 'relative' }}>
              <Avatar src={logoPath} variant='square' />
              {isComingSoon && (
                <Chip
                  label={t('status.comingSoon')}
                  color='primary'
                  size='small'
                  sx={{
                    position: 'absolute',
                    top: -10,
                    right: -10,
                    fontSize: '0.625rem',
                  }}
                  clickable={false}
                  onClick={() => {}}
                />
              )}
              {isInstalled && (
                <Chip
                  label={t('status.installed')}
                  color='success'
                  size='small'
                  sx={{
                    position: 'absolute',
                    top: -10,
                    right: -10,
                    fontSize: '0.625rem',
                  }}
                  clickable={false}
                  onClick={() => {}}
                />
              )}
            </Box>
            <Stack spacing={1}>
              <Typography align='center' variant='h5'>
                {integration.title}
              </Typography>
              <Typography
                align='center'
                variant='body2'
                sx={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                }}
              >
                {t(`descriptions.${integration.id.toLowerCase()}` as any)}
              </Typography>
            </Stack>
          </Stack>
        </CardContent>
        <Divider />
        <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
          <Button
            component={Link}
            href={`/app/settings/integrations/details?id=${integration.id}`}
            startIcon={<ArrowSquareOutIcon fontSize='var(--icon-fontSize-md)' />}
            size='small'
          >
            {t('actions.details')}
          </Button>
          {renderActionButton(integration)}
        </CardActions>
      </Card>
    );
  };

  const isIntegrationInstalling = (integrationId: string) => {
    return installingIds.includes(integrationId);
  };

  // Render the action button (install or uninstall)
  const renderActionButton = (integration: CombinedIntegration) => {
    const isComingSoon = integration.status === 'coming-soon';
    // Use the 'installed' property from CombinedIntegration
    const isInstalled = integration.installed;
    const isInstalling = isIntegrationInstalling(integration.id);

    if (isInstalled) {
      return (
        <Button
          component={Link}
          startIcon={<PlugsIcon fontSize='var(--icon-fontSize-md)' />}
          href={`${paths.settings.integrations.details}?id=${integration.id}#danger-zone-uninstall`}
          color='error'
          size='small'
          disabled={isComingSoon}
        >
          {t('actions.uninstall')}
        </Button>
      );
    } else {
      return (
        <Button
          color='primary'
          startIcon={isInstalling ? null : <PlugsConnectedIcon fontSize='var(--icon-fontSize-md)' />}
          onClick={() => handleInstall?.(integration)}
          size='small'
          disabled={isComingSoon || isInstalling}
          variant='contained'
        >
          {isInstalling ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CircularProgress size={16} color='inherit' sx={{ mr: 1 }} />
              {t('actions.installing')}
            </Box>
          ) : (
            t('actions.install')
          )}
        </Button>
      );
    }
  };

  // Render the card view
  const renderCardView = () => {
    return (
      <Grid container spacing={3}>
        {integrations.map((integration) => (
          <Grid key={integration.id} size={{ lg: 4, md: 6, xs: 12 }}>
            {renderCard(integration)}
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render the table view
  const renderTableView = () => {
    return (
      <Card>
        <Box sx={{ overflowX: 'auto' }}>
          <Table sx={{ minWidth: '800px' }}>
            <TableHead>
              <TableRow>
                <TableCell>{t('table.name')}</TableCell>
                <TableCell>{t('table.description')}</TableCell>
                <TableCell>{t('table.status')}</TableCell>
                <TableCell align='right'>{t('table.actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {integrations.map((integration) => {
                const isComingSoon = integration.status === 'coming-soon';
                // Use the 'installed' property from CombinedIntegration
                const isInstalled = integration.installed;

                const useDarkLogo = colorScheme === 'dark' && integration.darkLogo;

                const logoPath = `/assets/logos/${integration.id}${useDarkLogo ? '-dark' : ''}.svg`;

                return (
                  <TableRow hover key={integration.id}>
                    <TableCell>
                      <Stack sx={{ alignItems: 'center' }} direction='row' spacing={2}>
                        <Avatar src={logoPath} variant='square' />
                        <Typography variant='subtitle2'>{integration.title}</Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant='body2'
                        sx={{
                          maxWidth: '400px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {t(`descriptions.${integration.id.toLowerCase()}` as any)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {isComingSoon && (
                        <Chip
                          label={t('status.comingSoon')}
                          color='primary'
                          size='small'
                          clickable={false}
                          onClick={() => {}}
                        />
                      )}
                      {isInstalled && (
                        <Chip
                          label={t('status.installed')}
                          color='success'
                          size='small'
                          clickable={false}
                          onClick={() => {}}
                        />
                      )}
                      {!isComingSoon && !isInstalled && (
                        <Chip
                          label={t('status.available')}
                          color='default'
                          size='small'
                          clickable={false}
                          onClick={() => {}}
                        />
                      )}
                    </TableCell>
                    <TableCell align='right'>
                      <Stack direction='row' spacing={1} justifyContent='flex-end'>
                        <Button
                          component={Link}
                          href={`/app/settings/integrations/details?id=${integration.id}`}
                          startIcon={<ArrowSquareOutIcon fontSize='var(--icon-fontSize-md)' />}
                          size='small'
                        >
                          {t('actions.details')}
                        </Button>
                        {renderActionButton(integration)}
                      </Stack>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </Box>
      </Card>
    );
  };

  // Render skeletons if loading, otherwise real views
  if (isLoading) {
    return viewMode === 'card' ? renderCardSkeleton() : renderTableSkeleton();
  }
  // Render the appropriate view based on the viewMode
  return viewMode === 'card' ? renderCardView() : renderTableView();
}
