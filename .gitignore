# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
TestResults

# production
/build
out
.next

# misc
.DS_Store
.eslintcache
.idea
/.env
/.env.local
/.env.development.local
/.env.test.local
/.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Int
messages/*.d.json.ts

# Prisma
generated/prisma/

# TS
tsconfig.tsbuildinfo

# Logs
*.log