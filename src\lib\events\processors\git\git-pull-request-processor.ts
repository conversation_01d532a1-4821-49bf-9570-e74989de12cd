import { eventProcessorRegistry } from '@/lib/events/processors/registry';
import { GitPullRequestEvent, GitPullRequestEventAction } from '@/lib/events/types/git';
import { logger } from '@/lib/logger/default-logger';
import { createIntegrationProfile } from '@/lib/profile/integration-profile-creator';
import { db } from '@/services/db';
import { GitPullRequestState, IntegrationProfile, Prisma } from '@prisma/client';
import { upsertRepository } from './git-repository-processor';

// Register the pull request event processor
eventProcessorRegistry.register(GitPullRequestEvent, async (event) => {
  logger.info(`Processing ${event.id} event for pull request in repository ${event.repository.name}...`);

  const workspaceId = event.workspaceId;
  if (!workspaceId) {
    throw new Error('Workspace ID is required for pull request event processing');
  }

  const integrationId = event.integrationId;
  if (!integrationId) {
    throw new Error('Integration ID is required for pull request event processing');
  }

  if (!event.author) {
    throw new Error('Author is required for pull request events');
  }

  if (!event.pullRequest.idOnChannel) {
    throw new Error('Pull request ID on channel is required for pull request event processing');
  }

  try {
    // Find or create the repository
    const repository = await upsertRepository({
      workspaceId,
      integrationId,
      repository: event.repository,
    });

    // Find or create the profiles
    const eventAuthor = await createIntegrationProfile(event.author);
    const pullRequestUser = await createIntegrationProfile(event.pullRequestUser);

    // Determine pull request state and update data based on event action
    const { updateData } = determinePullRequestUpdate(event, eventAuthor);

    const state = event.pullRequest.state;

    // Create or update the pull request
    await db.gitPullRequest.upsert({
      where: {
        unique: {
          idOnChannel: event.pullRequest.idOnChannel,
          repositoryId: repository.id,
        },
      },
      update: {
        ...updateData,
        title: event.pullRequest.title,
        description: event.pullRequest.description,
        url: event.pullRequest.url,
        number: event.pullRequest.number,
        isDraft: event.pullRequest.isDraft,
        addedLines: event.pullRequest.addedLines,
        deletedLines: event.pullRequest.deletedLines,
        closedAt: event.pullRequest.closedAt,
        mergedAt: event.pullRequest.mergedAt,
        closedById: updateData.closedById ? (updateData.closedById as string) : null,
        authorId: pullRequestUser.id,
        createdAt: event.pullRequest.createdAt || event.timestamp,
        state: state || GitPullRequestState.OPEN,
        baseRef: event.pullRequest.baseRef,
        headRef: event.pullRequest.headRef,
        baseSha: event.pullRequest.baseSha,
        headSha: event.pullRequest.headSha,
      },
      create: {
        closedById: updateData.closedById ? (updateData.closedById as string) : null,
        idOnChannel: event.pullRequest.idOnChannel,
        repositoryId: repository.id,
        description: event.pullRequest.description,
        url: event.pullRequest.url,
        number: event.pullRequest.number,
        closedAt: event.pullRequest.closedAt,
        mergedAt: event.pullRequest.mergedAt,
        addedLines: event.pullRequest.addedLines || 0,
        deletedLines: event.pullRequest.deletedLines || 0,
        authorId: pullRequestUser.id,
        createdAt: event.pullRequest.createdAt || event.timestamp,
        isDraft: event.pullRequest.isDraft || false,
        state: state || GitPullRequestState.OPEN,
        title: event.pullRequest.title || '',
        baseRef: event.pullRequest.baseRef || '',
        headRef: event.pullRequest.headRef || '',
        baseSha: event.pullRequest.baseSha || '',
        headSha: event.pullRequest.headSha || '',
      },
    });

    logger.info(`Successfully processed ${event.id} event for pull request in repository ${event.repository.name}`);
  } catch (error) {
    // We don't throw the error further to prevent event processing from failing completely
    logger.error(
      `Error processing pull request for repository ${event.repository.name} in workspace ${workspaceId}:`,
      error
    );
  }
});

/**
 * Helper function to determine pull request update fields based on the event action.
 */
function determinePullRequestUpdate(
  event: GitPullRequestEvent,
  eventAuthor: IntegrationProfile
): {
  updateData: Prisma.GitPullRequestUncheckedUpdateInput;
} {
  const updateData: Prisma.GitPullRequestUncheckedUpdateInput = {};

  if (event.pullRequestEventAction === GitPullRequestEventAction.Closed) {
    updateData.closedById = eventAuthor.id;
  } else if (event.pullRequestEventAction === GitPullRequestEventAction.Merged) {
    updateData.closedById = eventAuthor.id;
  }

  return { updateData };
}
