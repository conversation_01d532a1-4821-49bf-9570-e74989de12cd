import Card from '@mui/material/Card';
import InputAdornment from '@mui/material/InputAdornment';
import OutlinedInput from '@mui/material/OutlinedInput';
import { MagnifyingGlassIcon } from '@phosphor-icons/react/dist/ssr/MagnifyingGlass';
import { useTranslations } from 'next-intl';
import * as React from 'react';

export function CustomersFilters(): React.JSX.Element {
  const t = useTranslations('customer');

  return (
    <Card sx={{ p: 2 }}>
      <OutlinedInput
        defaultValue=''
        fullWidth
        placeholder={t('search')}
        startAdornment={
          <InputAdornment position='start'>
            <MagnifyingGlassIcon fontSize='var(--icon-fontSize-md)' />
          </InputAdornment>
        }
        sx={{ maxWidth: '500px' }}
      />
    </Card>
  );
}
