'use client';

import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import type { SxProps } from '@mui/material/styles';
import { alpha, useTheme } from '@mui/material/styles';
import { ArrowClockwiseIcon } from '@phosphor-icons/react/dist/ssr/ArrowClockwise';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import type { ApexOptions } from 'apexcharts';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

export interface CommitActivityProps {
  chartSeries: { name: string; data: number[] }[];
  sx?: SxProps;
}

export function Sales({ chartSeries, sx }: CommitActivityProps): React.JSX.Element {
  const chartOptions = useChartOptions();
  const t = useTranslations('git.overview');

  return (
    <Card sx={sx}>
      <CardHeader
        action={
          <Button color='inherit' size='small' startIcon={<ArrowClockwiseIcon fontSize='var(--icon-fontSize-md)' />}>
            {t('commitActivity.sync')}
          </Button>
        }
        title={`${t('commitActivity.title')} (${t('timeframes.lastDays', { days: 30 })})`}
      />
      <CardContent>
        <Chart height={350} options={chartOptions} series={chartSeries} type='bar' width='100%' />
      </CardContent>
      <Divider />
      <CardActions sx={{ justifyContent: 'flex-end' }}>
        <Button color='inherit' endIcon={<ArrowRightIcon fontSize='var(--icon-fontSize-md)' />} size='small'>
          {t('commitActivity.overview')}
        </Button>
      </CardActions>
    </Card>
  );
}

function useChartOptions(): ApexOptions {
  const theme = useTheme();

  // Generate the last 30 days as categories
  const generateLast30Days = () => {
    const days = [];
    for (let i = 29; i >= 0; i--) {
      // Format as "Jun 1", "Jun 2", etc.
      const date = new Date();
      date.setDate(date.getDate() - i);
      const day = date.getDate();
      const month = date.toLocaleString('en-US', { month: 'short' });
      days.push(`${month} ${day}`);
    }
    return days;
  };

  return {
    chart: {
      background: 'transparent',
      stacked: false,
      toolbar: { show: false },
    },
    colors: [theme.palette.primary.main, alpha(theme.palette.primary.main, 0.25)],
    dataLabels: { enabled: false },
    fill: { opacity: 1, type: 'solid' },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
      xaxis: { lines: { show: false } },
      yaxis: { lines: { show: true } },
    },
    legend: { show: true, position: 'top' },
    plotOptions: { bar: { columnWidth: '12px' } },
    stroke: { colors: ['transparent'], show: true, width: 2 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: generateLast30Days(),
      labels: {
        offsetY: 5,
        style: { colors: theme.palette.text.secondary },
        rotate: -45,
        rotateAlways: false,
        hideOverlappingLabels: true,
        showDuplicates: false,
        trim: true,
        maxHeight: 120,
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => (value > 0 ? `${value}` : `${value}`),
        offsetX: -10,
        style: { colors: theme.palette.text.secondary },
      },
    },
    tooltip: {
      y: {
        formatter: (value) => `${value} commits`,
      },
    },
  };
}
