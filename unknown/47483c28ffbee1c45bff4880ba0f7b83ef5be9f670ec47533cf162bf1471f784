/* eslint-disable no-unused-vars */
export enum Theme {
  dark = 'dark',
  light = 'light',
  system = 'system',
}
/* eslint-enable no-unused-vars */

/**
 * Checks if the provided theme string is a valid Theme enum value.
 *
 * @param theme - The theme string to validate.
 * @returns A boolean indicating whether the theme is valid.
 */
export function isValidTheme(theme: string): theme is Theme {
  return Object.values(Theme).includes(theme as Theme);
}
