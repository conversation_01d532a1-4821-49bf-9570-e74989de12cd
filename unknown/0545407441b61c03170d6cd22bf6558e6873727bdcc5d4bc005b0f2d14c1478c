/**
 * @jest-environment node
 */

import { User } from '@prisma/client';
import { NextRequest } from 'next/server';

import { GET, PATCH } from '../route';

// Mock dependencies
jest.mock('@/services/firebase/admin', () => ({
  adminAuth: {
    verifyIdToken: jest.fn(),
  },
  adminStorage: {
    bucket: jest.fn().mockReturnValue({
      file: jest.fn().mockReturnValue({
        getSignedUrl: jest.fn(),
        exists: jest.fn(),
      }),
    }),
  },
}));

jest.mock('@/services/db', () => ({
  db: {
    user: {
      findFirst: jest.fn(),
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock('@/services/firebase/server-app', () => ({
  getAuthenticatedAppForUser: jest.fn(),
}));

import { db } from '@/services/db';
import { adminStorage } from '@/services/firebase/admin';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

const mockFindFirst = db.user.findFirst as jest.MockedFunction<typeof db.user.findFirst>;
const mockCreate = db.user.create as jest.MockedFunction<typeof db.user.create>;
const mockUpdate = db.user.update as jest.MockedFunction<typeof db.user.update>;

// Get the mocked functions from the mocked module
const mockBucket = adminStorage.bucket() as jest.MockedFunction<any>;
const mockFile = mockBucket.file() as jest.MockedFunction<any>;
const mockGetSignedUrl = mockFile.getSignedUrl as jest.MockedFunction<any>;
const mockExists = mockFile.exists as jest.MockedFunction<any>;

const mockGetAuthenticatedAppForUser = getAuthenticatedAppForUser as jest.MockedFunction<
  typeof getAuthenticatedAppForUser
>;

// Mock data
const mockUser: User = {
  id: 'user-1',
  displayName: 'Test User',
  email: '<EMAIL>',
  avatar: 'user-avatars/user-1/profile',
  onboarding: false,
  phone: null,
  country: null,
  timezone: 'UTC',
  language: 'en-US',
  theme: 'light',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

const createMockRequest = (method: string, body?: any, token?: string): NextRequest => {
  const headers = new Headers();
  if (token) {
    headers.set('authorization', `Bearer ${token}`);
  }
  if (body) {
    headers.set('content-type', 'application/json');
  }

  return new NextRequest(`http://localhost:3000/api/user`, {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined,
  });
};

describe('/api/user', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('should return user with signed URL when avatar exists', async () => {
      const mockSignedUrl = 'https://storage.googleapis.com/signed-url';

      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: { uid: 'user-1' },
      } as any);
      mockFindFirst.mockResolvedValue(mockUser);
      mockGetSignedUrl.mockResolvedValue([mockSignedUrl]);

      const request = createMockRequest('GET', null, 'valid-token');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.avatar).toBe(mockSignedUrl);
      expect(mockGetSignedUrl).toHaveBeenCalledWith({
        action: 'read',
        expires: expect.any(Number),
      });
    });

    it('should return user without signed URL when avatar is null', async () => {
      const userWithoutAvatar = { ...mockUser, avatar: null };

      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: { uid: 'user-1' },
      } as any);
      mockFindFirst.mockResolvedValue(userWithoutAvatar);

      const request = createMockRequest('GET', null, 'valid-token');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.avatar).toBeNull();
      expect(mockGetSignedUrl).not.toHaveBeenCalled();
    });

    it('should handle signed URL generation errors gracefully', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: { uid: 'user-1' },
      } as any);
      mockFindFirst.mockResolvedValue(mockUser);
      mockGetSignedUrl.mockRejectedValue(new Error('Storage error'));

      const request = createMockRequest('GET', null, 'valid-token');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.avatar).toBeNull();
    });
    it('should return 401 for invalid token', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue(null as any);

      const request = createMockRequest('GET', null, 'invalid-token');
      const response = await GET(request);

      expect(response.status).toBe(401);
    });

    it('should create new user if not found in database', async () => {
      const newUser = {
        id: 'user-1',
        displayName: 'New User',
        email: '<EMAIL>',
        avatar: '',
        phone: '',
        onboarding: true,
        country: null,
        timezone: null,
        language: 'en-US',
        theme: 'light',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: {
          uid: 'user-1',
          displayName: 'New User',
          email: '<EMAIL>',
          photoURL: null,
          phoneNumber: null,
        },
      } as any);
      mockFindFirst.mockResolvedValue(null);
      mockCreate.mockResolvedValue(newUser);

      const request = createMockRequest('GET', null, 'valid-token');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe('user-1');
      expect(mockCreate).toHaveBeenCalled();
    });
  });

  describe('PATCH', () => {
    it('should update user avatar successfully', async () => {
      const updateData = { avatar: 'user-avatars/user-1/new-profile' };
      const updatedUser = { ...mockUser, ...updateData };

      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: { uid: 'user-1' },
      } as any);
      mockUpdate.mockResolvedValue(updatedUser);
      mockGetSignedUrl.mockResolvedValue(['https://storage.googleapis.com/new-signed-url']);
      mockExists.mockResolvedValue([true]); // Ensure file.exists() returns true for this test

      const request = createMockRequest('PATCH', updateData, 'valid-token');
      const response = await PATCH(request);
      const responseBody = await response.json(); // Renamed to avoid conflict with 'data' from destructured user

      expect(response.status).toBe(200);
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { id: 'user-1' },
        data: updateData,
      });
      // The route now returns the direct path, not a signed URL for PATCH
      expect(responseBody.avatar).toBe('https://storage.googleapis.com/new-signed-url');
    });
    it('should remove user avatar successfully', async () => {
      const req = { json: async () => ({ avatar: '' }), headers: new Headers({ authorization: 'Bearer valid-token' }) };
      mockUpdate.mockResolvedValueOnce({ id: 'user-1', avatar: null } as any);

      // Mock getAuthenticatedAppForUser for this specific test case
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: { uid: 'user-1' },
      } as any);

      const response = await PATCH(req as NextRequest);
      const body = await response.json();

      expect(response.status).toBe(200);
      expect(body.avatar).toBeUndefined();
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { id: 'user-1' },
        data: { avatar: null }, // Corrected expectation
      });
    });
    it('should validate avatar path format', async () => {
      mockExists.mockResolvedValue([false]);

      const invalidUpdateData = { avatar: 'invalid-path' };

      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: { uid: 'user-1' },
      } as any);
      // Ensure db.user.update is not called when validation fails
      mockUpdate.mockClear();

      const request = createMockRequest('PATCH', invalidUpdateData, 'valid-token');
      const response = await PATCH(request);

      expect(response.status).toBe(400);
      expect(mockUpdate).not.toHaveBeenCalled(); // Verify that update was not called
    });
    it('should return 401 for invalid token', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue(null as any); // Fix: Use null instead of undefined

      const request = createMockRequest('PATCH', { avatar: 'test-path' }, 'invalid-token');
      const response = await PATCH(request);

      expect(response.status).toBe(401);
    });
  });
});
