import {
  Integration,
  IntegrationHandler,
  IntegrationInstallProps,
  IntegrationUninstallProps,
} from '@/services/integrations/integration';
import { IntegrationChannel } from '@prisma/client';

// Azure DevOps integration data
const azureDevOpsIntegration: Integration = {
  id: IntegrationChannel.AzureDevOps,
  title: 'Azure DevOps',
  status: 'coming-soon',
  capabilities: ['workItems', 'cicdPipelines', 'codeReview', 'repositoryAnalytics', 'teamPerformance', 'pullRequests'],
};

// Azure DevOps integration handler
export const azureDevOpsHandler: IntegrationHandler = {
  // Return the integration data
  getIntegration: () => {
    return azureDevOpsIntegration;
  },

  // Handle installation (not implemented for coming soon)
  onInstallAction: async (_props: IntegrationInstallProps) => {
    console.log('Installing Azure DevOps integration');
    // Not implemented for coming soon

    return false;
  },

  // Handle uninstallation (not implemented for coming soon)
  onUninstallAction: async (_props: IntegrationUninstallProps) => {
    console.log('Uninstalling Azure DevOps integration');
    // Not implemented for coming soon

    return false;
  },

  // This integration doesn't have a custom install flow
  hasCustomInstallFlow: () => false,
};
