'use client';

import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { IntegrationStatus } from '@prisma/client';
import { useTranslations } from 'next-intl';
import * as React from 'react';

interface ActionConfirmationDialogProps {
  open: boolean;
  actionType?: IntegrationStatus;
  accountLogin?: string;
  loading: boolean;
  onCloseAction: () => void;
  onConfirmAction: () => void;
}

export function ActionConfirmationDialog({
  open,
  actionType,
  accountLogin,
  loading,
  onCloseAction,
  onConfirmAction,
}: ActionConfirmationDialogProps): React.JSX.Element {
  const t = useTranslations('settings.integrations');
  const [accountNameInput, setAccountNameInput] = React.useState('');

  const isAccountNameValid = accountNameInput === accountLogin;

  // Reset input when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setAccountNameInput('');
    }
  }, [open]);
  const handleConfirm = () => {
    setAccountNameInput('');
    onConfirmAction();
  };

  return (
    <Dialog
      open={open}
      onClose={onCloseAction}
      maxWidth='sm'
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            boxShadow: (theme) => theme.shadows[10],
          },
        },
      }}
    >
      <DialogTitle sx={{ pb: 2 }}>
        <Typography variant='h5' component='div' sx={{ fontWeight: 600 }}>
          {t('github.action.title')}
        </Typography>
        <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
          {t('github.action.subTitle')}
        </Typography>
      </DialogTitle>
      <DialogContent sx={{ pt: 2 }}>
        <Stack spacing={3}>
          <Typography variant='body1'>
            {actionType === IntegrationStatus.Active
              ? t('github.action.activeDescription')
              : actionType === IntegrationStatus.Suspended
                ? t('github.action.suspendedDescription')
                : t('github.action.uninstalledDescription')}
          </Typography>

          <Alert severity='warning'>
            <Typography variant='body2'>
              {actionType === IntegrationStatus.Active
                ? t('github.action.activeWarning')
                : actionType === IntegrationStatus.Suspended
                  ? t('github.action.suspendedWarning')
                  : t('github.action.uninstalledWarning')}
            </Typography>
          </Alert>

          <Typography variant='body2'>
            {t('github.action.confirmationInstruction', { accountName: accountLogin || '' })}
          </Typography>

          <Box>
            <TextField
              fullWidth
              placeholder={accountLogin || ''}
              value={accountNameInput}
              onChange={(e) => setAccountNameInput(e.target.value)}
              error={accountNameInput.length > 0 && !isAccountNameValid}
              helperText={
                accountNameInput.length > 0 && !isAccountNameValid ? t('github.action.accountLoginMismatch') : ''
              }
              disabled={loading}
              sx={{ mt: 1 }}
            />
          </Box>
        </Stack>
      </DialogContent>{' '}
      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={onCloseAction} disabled={loading}>
          {t('github.action.cancelButton')}
        </Button>
        <Button
          variant='contained'
          color={
            actionType === IntegrationStatus.Active
              ? 'success'
              : actionType === IntegrationStatus.Suspended
                ? 'warning'
                : 'error'
          }
          sx={{ minWidth: 120 }}
          onClick={handleConfirm}
          disabled={loading || !isAccountNameValid}
          startIcon={loading ? <CircularProgress size={16} /> : null}
        >
          {loading
            ? t('github.action.loading')
            : actionType === IntegrationStatus.Active
              ? t('github.action.resumeButton')
              : actionType === IntegrationStatus.Suspended
                ? t('github.action.suspendButton')
                : t('github.action.uninstallButton')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
