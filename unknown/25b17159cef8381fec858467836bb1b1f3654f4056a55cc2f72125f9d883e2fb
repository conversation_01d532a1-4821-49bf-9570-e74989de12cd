import 'server-only';

import { User } from '@prisma/client';
import * as countries from 'i18n-iso-countries';
import { isValidPhoneNumber } from 'libphonenumber-js';
import { NextResponse } from 'next/server';

import { isValidLanguage } from '@/lib/models/language';
import { isValidTheme } from '@/lib/models/theme';
import { isValidTimezone } from '@/lib/models/timezone';
import { db } from '@/services/db';
import { adminStorage } from '@/services/firebase/admin';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * @swagger
 * /api/user:
 *   get:
 *     tags:
 *       - User
 *     summary: Get authenticated user
 *     description: Get the authenticated user
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: User data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function GET(_request: Request) {
  try {
    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser; // Check if the user exists in the database
    let user = await db.user.findFirst({
      where: {
        id: currentUser.uid,
      },
    });

    // If the user doesn't exist, create them
    if (!user) {
      user = await db.user.create({
        data: {
          id: currentUser.uid,
          displayName: currentUser.displayName || '',
          email: currentUser.email || '',
          avatar: currentUser.photoURL || '',
          phone: currentUser.phoneNumber || '',
          onboarding: true, // New users need to complete onboarding
        },
      });
    }

    // Generate signed URL for avatar if it exists and is a storage path
    let userWithSignedAvatar = { ...user };
    if (user.avatar && !user.avatar.startsWith('http') && user.avatar !== '') {
      try {
        const bucket = adminStorage.bucket();
        const file = bucket.file(user.avatar);
        const [signedUrls] = await file.getSignedUrl({
          action: 'read',
          expires: Date.now() + 15 * 60 * 1000, // 15 minutes from now
        });
        userWithSignedAvatar.avatar = signedUrls;
      } catch (error) {
        console.error('Error generating signed URL for avatar:', error);
        // If we can't generate a signed URL, set avatar to null so initials are shown
        userWithSignedAvatar.avatar = null;
      }
    }

    // Return the user data
    return NextResponse.json(userWithSignedAvatar);
  } catch (error) {
    console.error('Error getting/creating user:', error);
    return NextResponse.json({ error: 'Failed to get or create user' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/user:
 *   patch:
 *     tags:
 *       - User
 *     summary: Update user profile
 *     description: Update the current user's profile information
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               displayName:
 *                 type: string
 *                 description: User's last name
 *               country:
 *                 type: string
 *                 description: User's country code (e.g., 'US', 'BR')
 *               timezone:
 *                 type: string
 *                 description: User's timezone
 *               phone:
 *                 type: string
 *                 description: User's phone number
 *               language:
 *                 type: string
 *                 enum: [en-US, pt-BR, es]
 *                 description: User's preferred language
 *               theme:
 *                 type: string
 *                 enum: [light, dark, system]
 *                 description: User's preferred theme
 *     responses:
 *       200:
 *         description: User updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function PATCH(request: Request) {
  try {
    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Parse the request body
    const body = await request.json();

    let data: Partial<User> = {};

    // Validate required fields
    if (body.displayName) {
      if (body.displayName.trim() === '' || body.displayName.length > 70) {
        return NextResponse.json(
          { error: 'Invalid display name. Must be between 1 and 70 characters' },
          { status: 400 }
        );
      }

      data.displayName = body.displayName;
    }

    // Validate country if provided
    if (body.country) {
      if (!countries.getAlpha2Codes()[body.country]) {
        return NextResponse.json({ error: 'Invalid country' }, { status: 400 });
      }

      data.country = body.country;
    }

    // Validate language if provided
    if (body.language) {
      if (!isValidLanguage(body.language)) {
        return NextResponse.json({ error: 'Invalid language' }, { status: 400 });
      }

      data.language = body.language;
    }

    // Validate theme if provided
    if (body.theme) {
      if (!isValidTheme(body.theme)) {
        return NextResponse.json({ error: 'Invalid theme' }, { status: 400 });
      }

      data.theme = body.theme;
    }

    // Validate timezone if provided
    if (body.timezone) {
      if (!isValidTimezone(body.timezone)) {
        return NextResponse.json({ error: 'Invalid timezone' }, { status: 400 });
      }

      data.timezone = body.timezone;
    } // Validate phone if provided
    if (body.phone) {
      if (!isValidPhoneNumber(body.phone, body.country)) {
        return NextResponse.json({ error: 'Invalid phone number' }, { status: 400 });
      }
      data.phone = body.phone;
    }

    if (body.avatar !== undefined) {
      // Empty is allowed for removing avatar
      if (body.avatar == '') {
        data.avatar = null;
      } else if (body.avatar != null) {
        // Validate that the avatar file exists in Firebase Storage
        try {
          const bucket = adminStorage.bucket();
          const file = bucket.file(body.avatar);
          const result = await file.exists();

          if (!result || !result[0]) {
            return NextResponse.json({ error: 'Avatar file not found in storage' }, { status: 400 });
          }

          data.avatar = body.avatar;
        } catch (error) {
          console.error('Error validating avatar file:', error);
          return NextResponse.json({ error: 'Failed to validate avatar file' }, { status: 500 });
        }
      }
    }

    // Update the user in the database
    const user = await db.user.update({
      where: {
        id: currentUser.uid,
      },
      data: data,
    });

    let avatarUrl: string | undefined;
    if (user.avatar !== null && user.avatar !== '') {
      const bucket = adminStorage.bucket();
      const file = bucket.file(user.avatar);

      // 2 day from now
      const expires = new Date(Date.now() + 2 * 24 * 60 * 60 * 1000);

      const [url] = await file.getSignedUrl({
        version: 'v4',
        action: 'read',
        expires: expires,
      });

      avatarUrl = url;
    }

    return NextResponse.json({ ...user, avatar: avatarUrl });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
}
