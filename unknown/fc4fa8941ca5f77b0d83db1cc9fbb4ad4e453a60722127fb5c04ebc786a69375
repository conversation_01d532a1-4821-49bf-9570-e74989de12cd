import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * @swagger
 * /api/workspace/memberships:
 *   get:
 *     tags:
 *       - Workspace
 *     summary: Get workspace memberships
 *     description: Get all workspace memberships for the current user
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: The list of workspace memberships
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   userId:
 *                     $ref: '#/components/schemas/WorkspaceMembership/properties/userId'
 *                   workspaceId:
 *                     $ref: '#/components/schemas/WorkspaceMembership/properties/workspaceId'
 *                   roleId:
 *                     $ref: '#/components/schemas/WorkspaceMembership/properties/roleId'
 *                 additionalProperties: false
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
export async function GET(_request: NextRequest) {
  try {
    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Get all workspace memberships for the user
    const memberships = await db.workspaceMembership.findMany({
      where: {
        userId: currentUser.uid,
      },
    });

    return NextResponse.json(memberships);
  } catch (error) {
    console.error('Error getting workspace memberships:', error);
    return NextResponse.json({ error: 'Failed to get workspace memberships' }, { status: 500 });
  }
}
