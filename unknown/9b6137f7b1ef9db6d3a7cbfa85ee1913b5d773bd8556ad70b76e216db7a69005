import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { GitBranchIcon } from '@phosphor-icons/react/dist/ssr/GitBranch';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

import { config } from '@/config';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('git.repositories');

  return {
    title: `${t('title')} | ${config.site.name}`,
  };
}

export default async function Page() {
  const t = await getTranslations('git.repositories');

  return (
    <Stack spacing={3}>
      <Card>
        <CardHeader title={t('title')} />
        <CardContent>
          <Stack
            alignItems='center'
            direction='column'
            justifyContent='center'
            spacing={2}
            sx={{ minHeight: 200, textAlign: 'center' }}
          >
            <GitBranchIcon fontSize={48} />
            <Typography variant='h6'>Repositories Page</Typography>
            <Typography color='text.secondary' variant='body2'>
              This is a placeholder for the repositories page.
            </Typography>
          </Stack>
        </CardContent>
      </Card>
    </Stack>
  );
}
