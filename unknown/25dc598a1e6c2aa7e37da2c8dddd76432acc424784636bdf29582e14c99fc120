'use client';

import { useRouter } from '@/i18n/navigation';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import { getCookie } from 'cookies-next';
import * as React from 'react';

import { useWorkspace } from '@/contexts/workspace-context';
import { logger } from '@/lib/logger/default-logger';
import { paths } from '@/paths';

export interface WorkspaceGuardProps {
  children: React.ReactNode;
}

// TODO: with two browser tabs open, if a user is navigating on one workspace and the other is navigating on another, since we use cookies to store current workspace
// TODO: user may navigate on one workspace but with cookies pointing to the other workspace
// TODO: options:
//        1. when selecting workspace, make sure other tags will have an prompt asking if want to change workspace
//        2. put the workspace in the query parameters, and prioritize the workspace in the query parameters (need to check if nextjs have a native way of having a fixed query parameters between routing changes)
//        3. use other storage to keep track of the current workspace instead of cookies, and change apis (client and server side) to also handle that

export function WorkspaceGuard({ children }: WorkspaceGuardProps): React.JSX.Element {
  const router = useRouter();
  const { currentWorkspace, loading } = useWorkspace();

  // Check if there's a workspace cookie to determine initial state
  const hasWorkspaceCookie = React.useMemo(() => {
    try {
      const cookieValue = getCookie('selected-workspace');
      return Boolean(cookieValue);
    } catch {
      return false;
    }
  }, []);

  // Start with isChecking false if we have a workspace or a workspace cookie
  const [isChecking, setIsChecking] = React.useState<boolean>(() => {
    if (currentWorkspace || hasWorkspaceCookie) {
      logger.debug('WorkspaceGuard: Initial state - not checking (workspace available)', {
        hasCurrentWorkspace: Boolean(currentWorkspace),
        hasWorkspaceCookie,
        loading,
      });

      return false;
    }

    logger.debug('WorkspaceGuard: Initial state - checking required', {
      hasCurrentWorkspace: Boolean(currentWorkspace),
      hasWorkspaceCookie,
      loading,
    });

    return !loading;
  });
  const [retryCount] = React.useState<number>(0);

  React.useEffect(() => {
    logger.debug('WorkspaceGuard: Effect triggered', {
      loading,
      currentWorkspace: currentWorkspace?.id,
      isChecking,
      shouldRedirect: !loading && !currentWorkspace,
      retryCount,
      hasWorkspaceCookie,
    });

    if (loading) {
      logger.debug('WorkspaceGuard: Still loading, waiting...');

      setIsChecking(true);
      return;
    }

    if (!currentWorkspace) {
      // If we have a workspace cookie but no currentWorkspace, wait a bit more
      if (hasWorkspaceCookie && retryCount < 2) {
        logger.debug('WorkspaceGuard: Have cookie but no workspace, waiting for context to initialize...');
        setIsChecking(true);
        return;
      }

      logger.debug('WorkspaceGuard: No workspace selected, redirecting to workspace selection');

      setIsChecking(true);
      router.replace(paths.workspaceSelection);
      return;
    }

    logger.debug('WorkspaceGuard: Workspace found, clearing checking state');
    setIsChecking(false);
  }, [loading, currentWorkspace, router, retryCount, hasWorkspaceCookie, isChecking]);

  // Also reset isChecking when currentWorkspace changes (workspace selected)
  React.useEffect(() => {
    if (currentWorkspace) {
      logger.debug('WorkspaceGuard: Workspace changed, clearing checking state', { workspaceId: currentWorkspace.id });
      setIsChecking(false);
    }
  }, [currentWorkspace]);

  if (loading || isChecking) {
    logger.debug('WorkspaceGuard: Showing loading spinner', { loading, isChecking });

    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  logger.debug('WorkspaceGuard: Rendering children', { currentWorkspace: currentWorkspace?.id });

  return <React.Fragment>{children}</React.Fragment>;
}

export default WorkspaceGuard;
