/**
 * This file exports the appropriate API service based on the environment
 *
 * In client components, import from:
 * import { getApiService } from '@/services/api';
 *
 * In server components, import from:
 * import { getServerApiService } from '@/services/api/server';
 */

// Re-export the client API service as the default for client components
export { ClientApiService, getClientApiService as getApiService } from './client';

// Re-export the server API service for explicit server usage
export { ServerApiService, getServerApiService } from './server';

// Re-export the core types
export { CoreApiService } from './core';
export type { TokenProvider } from './core';

// Re-export the user API service
export { UserApiService, createUserApiService } from './user';

// Re-export the workspace API service
export { WorkspaceApiService, createWorkspaceApiService } from './workspace';

// Re-export the integration API service
export { IntegrationApiService, createIntegrationApiService } from './integration';
