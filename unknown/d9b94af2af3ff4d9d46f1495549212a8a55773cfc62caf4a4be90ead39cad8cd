import { logger } from './default-logger';

/**
 * Logs a request to the console.
 *
 * Must be used only for development purposes.
 *
 * @param req The request to log
 */
export async function logRequest(req: Request) {
  const method = req.method;
  const url = req.url;
  const headers = Object.fromEntries(req.headers.entries());
  let body = {};

  if (method !== 'GET') {
    try {
      body = await req.clone().json();
    } catch (_) {
      body = {};
    }
  }

  logger.debug(`
  ${method} ${url}
  Headers: ${JSON.stringify(headers)}
  Body: ${JSON.stringify(body)}
  `);
}
