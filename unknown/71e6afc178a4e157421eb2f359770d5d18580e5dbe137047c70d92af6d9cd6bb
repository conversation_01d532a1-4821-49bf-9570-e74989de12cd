import { azureDevOpsHandler } from './azure-devops';
import { bitbucketHandler } from './bitbucket';
import { githubHandler } from './github';
import { gitlabHandler } from './gitlab';
import { jiraHandler } from './jira';
import { integrationsRegistry } from './registry';
import { slackHandler } from './slack';

// Register all integrations
integrationsRegistry.register(githubHandler);
integrationsRegistry.register(jiraHandler);
integrationsRegistry.register(slackHandler);
integrationsRegistry.register(azureDevOpsHandler);
integrationsRegistry.register(bitbucketHandler);
integrationsRegistry.register(gitlabHandler);

// Add more integrations here as they become available

export { integrationsRegistry };
