{"i18n-ally.enabledFrameworks": ["next-intl"], "i18n-ally.dirStructure": "file", "i18n-ally.autoDetection": true, "i18n-ally.enabledParsers": ["json", "js", "ts"], "i18n-ally.keystyle": "nested", "i18n-ally.displayLanguage": "en-US", "i18n-ally.editor.preferEditor": true, "i18n-ally.extract.autoDetect": true, "i18n-ally.extract.keygenStyle": "camelCase", "i18n-ally.extract.keyMaxLength": 60, "i18n-ally.localesPaths": "messages/", "i18n-ally.namespace": true, "i18n-ally.translate.engines": ["deepl", "google-cn", "google"], "jest.rootPath": "./", "i18n-ally.sourceLanguage": "en-US", "typescript.tsdk": "node_modules\\typescript\\lib", "jest.runMode": {"type": "on-demand"}, "json.schemas": [{"fileMatch": ["firebase.json"], "url": "https://raw.githubusercontent.com/firebase/firebase-tools/master/schema/firebase-config.json"}]}