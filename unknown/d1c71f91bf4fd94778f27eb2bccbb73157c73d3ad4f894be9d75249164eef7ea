import { getPathname } from '@/i18n/navigation';
import { routing } from '@/i18n/routing';
import { MetadataRoute } from 'next';
import { Locale } from 'next-intl';

import { config } from '@/config';

export const dynamic = 'force-static';

export default function sitemap(): MetadataRoute.Sitemap {
  // Get all unique pathnames from routing configuration
  const pathnames = Object.keys(routing.pathnames);

  return pathnames.flatMap((pathname) => getEntries(pathname as Href));
}

type Href = Parameters<typeof getPathname>[0]['href'];

function getEntries(href: Href) {
  return routing.locales.map((locale) => ({
    url: getUrl(href, locale),
    alternates: {
      languages: Object.fromEntries(routing.locales.map((cur) => [cur, getUrl(href, cur)])),
    },
  }));
}

function getUrl(href: Href, locale: Locale) {
  let pathname = getPathname({ locale, href });

  // Remvoes / from start of pathname
  pathname = pathname.replace(/^\/+/, '');

  // join URL paths config.site.url, pathname
  return config.site.url + pathname;
}
