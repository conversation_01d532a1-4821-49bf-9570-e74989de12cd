import { Committer, User } from '@octokit/webhooks-types';
import { IntegrationChannel, IntegrationProfile } from '@prisma/client';

export function convertCommiterToUser(commiter: Committer | undefined): Partial<User> | undefined {
  if (!commiter) {
    return undefined;
  }

  return {
    login: commiter.username || null,
    name: commiter.name || null,
    email: commiter.email || null,
  } as Partial<User>;
}

export function convertToIntegrationProfile(data: {
  user: Partial<User>;
  additionalData?: Partial<User>;
  workspaceId: string;
}): Partial<IntegrationProfile> {
  const profile: Partial<IntegrationProfile> = {
    username: data.user.login || data.additionalData?.login || null,
    idOnChannel: data.user.id?.toString() || data.additionalData?.id?.toString() || null,
    name: data.user.name || data.additionalData?.name || null,
    emails: data.user.email ? [data.user.email] : [],
    channel: IntegrationChannel.GitHub,
    workspaceId: data.workspaceId,
  };

  if (data.additionalData) {
    if (data.additionalData.email && !profile.emails?.includes(data.additionalData.email)) {
      profile.emails!.push(data.additionalData.email);
    }
  }

  return profile;
}
