// Test the language enum
import { isValidLanguage, Language } from '../language';

describe('Language enum', () => {
  it('Language enum', () => {
    expect(Language.EN_US).toBe('en-US');
    expect(Language.PT_BR).toBe('pt-BR');
    expect(Language.ES).toBe('es');
  });

  it('isValidLanguage', () => {
    expect(isValidLanguage('en-US')).toBe(true);
    expect(isValidLanguage('pt-BR')).toBe(true);
    expect(isValidLanguage('es')).toBe(true);
    expect(isValidLanguage('invalid')).toBe(false);
  });
});
