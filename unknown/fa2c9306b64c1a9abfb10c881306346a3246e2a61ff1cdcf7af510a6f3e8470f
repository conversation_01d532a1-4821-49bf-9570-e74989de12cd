import { Prisma } from '@prisma/client';

/**
 * Determines if a given error is a unique constraint error.
 *
 * @param error Error to determine.
 * @returns True if error is a unique constraint error, false otherwise.
 */
export function isUniqueConstraintError(error: any): boolean {
  if (!(error instanceof Prisma.PrismaClientKnownRequestError)) {
    return false;
  }

  return error.code === 'P2002';
}
