import { get<PERSON><PERSON>ie } from 'cookies-next';

import { CoreApiService, TokenProvider, WorkspaceProvider } from './core';

/**
 * Server-side token provider that gets the token from cookies
 */
class ServerTokenProvider implements TokenProvider {
  /**
   * Get the authentication token from cookies
   * @returns A promise that resolves to the token or undefined
   */
  async getToken(): Promise<string | undefined> {
    try {
      return getCookie('__session');
    } catch (error) {
      console.error('Error getting session token from cookies:', error);
      return undefined;
    }
  }
}

/**
 * Server-side workspace provider that gets the workspace ID from cookies
 */
class ServerWorkspaceProvider implements WorkspaceProvider {
  /**
   * Get the current workspace ID from cookies
   * @returns A promise that resolves to the workspace ID or undefined
   */
  async getWorkspaceId(): Promise<string | undefined> {
    try {
      const workspaceId = getCookie('selected-workspace');
      return workspaceId?.toString();
    } catch (error) {
      console.error('Error getting workspace ID from cookies:', error);
      return undefined;
    }
  }
}

/**
 * Server-side API service
 * This service is used in server components and gets the token from cookies
 */
export class ServerApiService extends CoreApiService {
  /**
   * Create a new ServerApiService
   * @param baseURL Optional base URL for API requests
   */
  constructor(baseURL?: string) {
    super(new ServerTokenProvider(), new ServerWorkspaceProvider(), baseURL);
  }
}

/**
 * Get the server API service instance
 * Note: We don't use a singleton here because server components
 * might be rendered in different contexts with different cookies
 * @param baseURL Optional base URL for API requests
 * @returns A new server API service instance
 */
export function getServerApiService(baseURL?: string): ServerApiService {
  return new ServerApiService(baseURL);
}

export default getServerApiService;
