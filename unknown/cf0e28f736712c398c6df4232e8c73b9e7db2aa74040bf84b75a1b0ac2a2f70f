'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import { EyeIcon } from '@phosphor-icons/react/dist/ssr/Eye';
import { EyeClosedIcon } from '@phosphor-icons/react/dist/ssr/EyeClosed';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { useAuth } from '@/contexts/firebase-auth-context';
import { logger } from '@/lib/logger/default-logger';

type Values = {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
};

const defaultValues = {
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
} satisfies Values;

export function UpdatePasswordForm(): React.JSX.Element {
  const [isPending, setIsPending] = React.useState<boolean>(false);
  const [isSuccess, setIsSuccess] = React.useState<boolean>(false);
  const [showCurrentPassword, setShowCurrentPassword] = React.useState<boolean>(false);
  const [showNewPassword, setShowNewPassword] = React.useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState<boolean>(false);
  const [errorDismissed, setErrorDismissed] = React.useState<boolean>(false);
  const [formErrorDismissed, setFormErrorDismissed] = React.useState<boolean>(false);
  const { updatePassword, clearError, error: authError } = useAuth();
  const t = useTranslations('settings.password');

  // Create schema with translations
  const schema = React.useMemo(() => {
    return zod
      .object({
        currentPassword: zod.string().min(1, { message: t('currentPasswordRequired') }),
        newPassword: zod.string().min(6, { message: t('minPasswordLength') }),
        confirmPassword: zod.string().min(1, { message: t('confirmPasswordRequired') }),
      })
      .refine((data) => data.newPassword === data.confirmPassword, {
        message: t('passwordMismatch'),
        path: ['confirmPassword'],
      });
  }, [t]);

  const {
    control,
    handleSubmit,
    setError,
    reset,
    formState: { errors },
  } = useForm<Values>({ defaultValues, resolver: zodResolver(schema) });

  const onSubmit = React.useCallback(
    async (values: Values): Promise<void> => {
      setIsPending(true);
      // Reset error dismissed states on new submission
      setErrorDismissed(false);
      setFormErrorDismissed(false);
      // Clear any existing auth context errors
      clearError();

      try {
        await updatePassword(values.currentPassword, values.newPassword);
        setIsPending(false);
        setIsSuccess(true);
        reset(); // Clear the form
      } catch (error) {
        // Parse Firebase error message and provide a user-friendly message
        let errorMessage = t('errors.updateFailed');

        // Only in development, log the actual error
        logger.warn('Password update error:', error);

        // Handle specific error codes
        const firebaseError = error as { code?: string; message: string };
        if (firebaseError.code === 'auth/invalid-credential') {
          errorMessage = t('errors.wrongPassword');
        } else if (firebaseError.code === 'auth/weak-password') {
          errorMessage = t('errors.weakPassword');
        } else if (firebaseError.code === 'auth/requires-recent-login') {
          errorMessage = t('errors.requiresRecentLogin');
        }

        setError('root', { type: 'server', message: errorMessage });
        setIsPending(false);
      }

      // Success message is handled in the try/catch block
    },
    [updatePassword, setError, setErrorDismissed, setFormErrorDismissed, t, reset, clearError]
  );

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card>
        <CardHeader subheader={t('subheader')} title={t('title')} />
        <Divider />
        <CardContent>
          <Stack spacing={3} sx={{ maxWidth: 'sm' }}>
            {/* Success Alert */}
            {isSuccess && (
              <Alert severity='success' onClose={() => setIsSuccess(false)}>
                {t('successMessage')}
              </Alert>
            )}

            {/* Auth Error Alert */}
            {authError && !errorDismissed && (
              <Alert
                severity='error'
                onClose={() => {
                  setErrorDismissed(true);
                  clearError();
                }}
              >
                {authError}
              </Alert>
            )}

            {/* Form Error Alert */}
            {errors.root && !formErrorDismissed && (
              <Alert severity='error' onClose={() => setFormErrorDismissed(true)}>
                {errors.root.message}
              </Alert>
            )}

            {/* Current Password Field */}
            <Controller
              control={control}
              name='currentPassword'
              render={({ field }) => (
                <FormControl fullWidth error={Boolean(errors.currentPassword)}>
                  <InputLabel>{t('currentPassword')}</InputLabel>
                  <OutlinedInput
                    {...field}
                    disabled={isPending}
                    label={t('currentPassword')}
                    type={showCurrentPassword ? 'text' : 'password'}
                    endAdornment={
                      <InputAdornment position='end'>
                        <IconButton
                          aria-label='toggle current password visibility'
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          edge='end'
                        >
                          {showCurrentPassword ? <EyeClosedIcon /> : <EyeIcon />}
                        </IconButton>
                      </InputAdornment>
                    }
                  />
                  {errors.currentPassword ? <FormHelperText>{errors.currentPassword.message}</FormHelperText> : null}
                </FormControl>
              )}
            />

            {/* New Password Field */}
            <Controller
              control={control}
              name='newPassword'
              render={({ field }) => (
                <FormControl fullWidth error={Boolean(errors.newPassword)}>
                  <InputLabel>{t('newPassword')}</InputLabel>
                  <OutlinedInput
                    {...field}
                    disabled={isPending}
                    label={t('newPassword')}
                    type={showNewPassword ? 'text' : 'password'}
                    endAdornment={
                      <InputAdornment position='end'>
                        <IconButton
                          aria-label='toggle new password visibility'
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          edge='end'
                        >
                          {showNewPassword ? <EyeClosedIcon /> : <EyeIcon />}
                        </IconButton>
                      </InputAdornment>
                    }
                  />
                  {errors.newPassword ? <FormHelperText>{errors.newPassword.message}</FormHelperText> : null}
                </FormControl>
              )}
            />

            {/* Confirm Password Field */}
            <Controller
              control={control}
              name='confirmPassword'
              render={({ field }) => (
                <FormControl fullWidth error={Boolean(errors.confirmPassword)}>
                  <InputLabel>{t('confirmPassword')}</InputLabel>
                  <OutlinedInput
                    {...field}
                    disabled={isPending}
                    label={t('confirmPassword')}
                    type={showConfirmPassword ? 'text' : 'password'}
                    endAdornment={
                      <InputAdornment position='end'>
                        <IconButton
                          aria-label='toggle confirm password visibility'
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          edge='end'
                        >
                          {showConfirmPassword ? <EyeClosedIcon /> : <EyeIcon />}
                        </IconButton>
                      </InputAdornment>
                    }
                  />
                  {errors.confirmPassword ? <FormHelperText>{errors.confirmPassword.message}</FormHelperText> : null}
                </FormControl>
              )}
            />
          </Stack>
        </CardContent>
        <Divider />
        <CardActions sx={{ justifyContent: 'flex-end' }}>
          <Button disabled={isPending} type='submit' variant='contained'>
            {isPending ? 'Updating...' : t('update')}
          </Button>
        </CardActions>
      </Card>
    </form>
  );
}
