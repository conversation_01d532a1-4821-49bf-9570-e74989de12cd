'use client';

import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Checkbox from '@mui/material/Checkbox';
import Divider from '@mui/material/Divider';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormGroup from '@mui/material/FormGroup';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useTranslations } from 'next-intl';
import * as React from 'react';

export function Notifications(): React.JSX.Element {
  const t = useTranslations('settings.notifications');

  return (
    <form
      onSubmit={(event) => {
        event.preventDefault();
      }}
    >
      <Card>
        <CardHeader subheader={t('subheader')} title={t('title')} />
        <Divider />
        <CardContent>
          <Grid container spacing={6} wrap='wrap'>
            <Grid size={{ md: 4, sm: 6, xs: 12 }}>
              <Stack spacing={1}>
                <Typography variant='h6'>{t('email')}</Typography>
                <FormGroup>
                  <FormControlLabel control={<Checkbox defaultChecked />} label={t('productUpdates')} />
                  <FormControlLabel control={<Checkbox />} label={t('securityUpdates')} />
                </FormGroup>
              </Stack>
            </Grid>
            <Grid size={{ md: 4, sm: 6, xs: 12 }}>
              <Stack spacing={1}>
                <Typography variant='h6'>{t('phone')}</Typography>
                <FormGroup>
                  <FormControlLabel control={<Checkbox defaultChecked />} label={t('productUpdates')} />
                  <FormControlLabel control={<Checkbox />} label={t('securityUpdates')} />
                </FormGroup>
              </Stack>
            </Grid>
          </Grid>
        </CardContent>
        <Divider />
        <CardActions sx={{ justifyContent: 'flex-end' }}>
          <Button variant='contained'>{t('saveChanges')}</Button>
        </CardActions>
      </Card>
    </form>
  );
}
