'use client';

import { getAuth } from 'firebase/auth';

import { ClientApiService, getClientApiService } from '../client';
import { CoreApiService } from '../core';

// Mock Firebase Auth
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn().mockReturnValue({
    currentUser: {
      getIdToken: jest.fn().mockResolvedValue('mock-id-token'),
    },
  }),
}));

// Create a mock CoreApiService class
class MockCoreApiService {
  get = jest.fn();
  post = jest.fn();
  put = jest.fn();
  patch = jest.fn();
  delete = jest.fn();
  request = jest.fn();
}

// Mock the CoreApiService
jest.mock('../core', () => {
  return {
    CoreApiService: jest.fn().mockImplementation(() => {
      return new MockCoreApiService();
    }),
  };
});

describe('ClientApiService', () => {
  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
  });

  it('should create a ClientApiService instance', () => {
    const apiService = new ClientApiService();
    expect(apiService).toBeDefined();
    expect(CoreApiService).toHaveBeenCalled();
  });

  it('should return a singleton instance', () => {
    const instance1 = getClientApiService();
    const instance2 = getClientApiService();

    expect(instance1).toBe(instance2);
    expect(CoreApiService).toHaveBeenCalledTimes(1);
  });

  it('should get token from Firebase Auth', async () => {
    // Create a new instance of ClientApiService
    new ClientApiService();

    // Extract the token provider from the constructor call
    const tokenProvider = (CoreApiService as jest.Mock).mock.calls[0][0];

    // Call the getToken method
    const token = await tokenProvider.getToken();

    // Verify that getAuth was called
    expect(getAuth).toHaveBeenCalled();

    // Verify that the token is correct
    expect(token).toBe('mock-id-token');
  });
});
