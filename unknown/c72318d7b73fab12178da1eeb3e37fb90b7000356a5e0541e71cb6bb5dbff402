import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger/default-logger';
import { resolveURL } from '@/lib/proxy';
import { paths } from '@/paths';
import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import { getInstance } from '@/services/github/app';
import { GitHubAccountType, IntegrationChannel, IntegrationStatus, Permission } from '@prisma/client';

// TODO: improve user experience on errors on this API.
// Since user is redirected to this URL, it will be bad to show JSON errors.
// Maybe redirect it to an error page that will show errors to the user (like the 404 page, but for specific errors)
// We could define a enum with all possible errors and show the user errors based on that, and make this api redirect user to the error page with this value as query parameter
// For now, since they are exceptions, we will return json errors

// TODO: deal with admin request
// http://localhost:3000/api/integration/callback/github?setup_action=request&state=007e0a44-a9d6-49b4-8c53-966d5147df2f
// TODO: deal with updates (redirect user to the workspace-selection to make sure it will have a workspace selected to install on a specific workspace)

// DOCUMENT WITH SWAGGER JSDOC

/**
 * GitHub integration callback
 *
 * This endpoint is used to complete the installation of the GitHub integration.
 * It is called by GitHub after the installation is complete.
 *
 * @swagger
 * /api/integration/callback/github:
 *   get:
 *     tags:
 *       - Integration
 *     summary: Complete GitHub installation
 *     description: Complete the installation of the GitHub integration.
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: GitHub integration
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/GitHubIntegration'
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 */
export async function GET(request: NextRequest) {
  // Get the authenticated user from Firebase
  const authResult = await getAuthenticatedAppForUser();

  // If no user is not authenticated, redirect to the sign-in page
  if (!authResult?.currentUser) {
    logger.info('User not authenticated, redirecting to sign-in page');

    const url = new URL(paths.auth.signIn, resolveURL(request));
    url.searchParams.set('returnTo', resolveURL(request).toString());

    return NextResponse.redirect(url);
  }

  const installationIdParam = request.nextUrl.searchParams.get('installation_id');
  const installationId = installationIdParam ? parseInt(installationIdParam) : null;
  const setupAction = request.nextUrl.searchParams.get('setup_action');
  const stateId = request.nextUrl.searchParams.get('state');

  if (!installationId || !setupAction || !stateId) {
    return NextResponse.json({ message: 'Missing required parameters' }, { status: 400 });
  }

  try {
    const currentUser = authResult.currentUser;

    const state = await db.integrationState.findFirst({
      where: {
        id: stateId,
      },
      select: {
        workspaceId: true,
      },
    });

    if (!state || !state.workspaceId) {
      return NextResponse.json({ message: 'Invalid state' }, { status: 400 });
    }

    // Check if the user can manage integrations
    const havePermission = await db.workspaceMembership
      .findFirst({
        where: {
          userId: currentUser.uid,
          workspaceId: state.workspaceId,
          role: {
            permissions: {
              some: {
                id: Permission.MANAGE_INTEGRATIONS,
              },
            },
          },
        },
      })
      .then((r) => Boolean(r));

    if (!havePermission) {
      return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    }

    const { data, status } = await getInstance().octokit.request('GET /app/installations/{installation_id}', {
      installation_id: installationId,
    });

    if (!data || !data.account || status !== 200) {
      return NextResponse.json({ message: 'Invalid installation' }, { status: 400 });
    }

    const account = data.account;
    const accountType = 'type' in account! ? (account.type as GitHubAccountType) : GitHubAccountType.User;
    const login = 'login' in account! ? account.login : account!.slug;

    if (!login) {
      throw new Error('GitHub installation does not have a login');
    }

    await db.$transaction(async (tx) => {
      // Check if this installation already exists for this workspace
      const existingIntegration = await tx.workspaceIntegration.findFirst({
        where: {
          workspaceId: state.workspaceId,
          channel: IntegrationChannel.GitHub,
          github: {
            installationId: installationId,
          },
        },
        include: {
          github: true,
        },
      });

      if (existingIntegration) {
        logger.info('GitHub installation already exists for workspace', {
          workspaceId: state.workspaceId,
          installationId,
          userId: currentUser.uid,
        });

        return existingIntegration;
      }

      // Create a new WorkspaceIntegration for this specific GitHub installation
      const integration = await tx.workspaceIntegration.create({
        data: {
          workspaceId: state.workspaceId,
          channel: IntegrationChannel.GitHub,
          status: IntegrationStatus.Synchronizing,
          integrationIdOnChannel: installationId.toString(),
          github: {
            create: {
              installationId,
              accountType: accountType,
              accountId: data.account!.id,
              accountLogin: login,
            },
          },
        },
      });

      logger.info('GitHub integration created successfully', {
        workspaceId: state.workspaceId,
        integrationId: integration.id,
        installationId,
        userId: currentUser.uid,
      });

      return integration;
    });

    await db.integrationState.delete({
      where: {
        id: stateId,
      },
    });

    return NextResponse.redirect(
      new URL(paths.settings.integrations.details + '?id=' + IntegrationChannel.GitHub, resolveURL(request))
    );
  } catch (error) {
    logger.error('Error registering GitHub integration', error);

    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
