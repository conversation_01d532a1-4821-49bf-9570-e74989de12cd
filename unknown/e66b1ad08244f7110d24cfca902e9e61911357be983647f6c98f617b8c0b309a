'use client';

import { useRouter } from '@/i18n/navigation';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Snackbar from '@mui/material/Snackbar';
import Typography from '@mui/material/Typography';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { useAuth } from '@/contexts/firebase-auth-context';
import { useCurrentUser } from '@/contexts/user-context';
import { logger } from '@/lib/logger/default-logger';
import { paths } from '@/paths';

export interface OnboardingGuardProps {
  children: React.ReactNode;
  /**
   * The type of guard to use
   * - 'onboarding': Redirects to onboarding if user needs to complete it (used in authenticated layout)
   * - 'onboardingRequired': Redirects to home if user has already completed onboarding (used in onboarding layout)
   */
  type: 'onboarding' | 'onboardingRequired';
}

export function OnboardingGuard({ children, type }: OnboardingGuardProps): React.JSX.Element {
  const t = useTranslations('auth.guard');
  const router = useRouter();
  const { user: authUser, loading: authLoading } = useAuth();
  const { user: dbUser, loading: userLoading, refreshing: userRefreshing, error: userError } = useCurrentUser();
  const [isChecking, setIsChecking] = React.useState<boolean>(true);
  const [retryCountdown, setRetryCountdown] = React.useState<number | null>(null);
  const [retryAttempt, setRetryAttempt] = React.useState<number>(0);

  // Calculate backoff time based on retry attempts (max 60 seconds)
  const getBackoffTime = React.useCallback((attempt: number): number => {
    // Start with 5 seconds, then double each time up to 60 seconds
    const backoffTime = Math.min(5 * Math.pow(2, attempt), 60);
    console.log(`Guard calculated backoff time for attempt ${attempt}: ${backoffTime} seconds`);
    return backoffTime;
  }, []);

  // Track the next retry time
  const [nextRetryTime, setNextRetryTime] = React.useState<number | null>(null);

  // Get the fetchUser function from the user context
  const { fetchUser: contextFetchUser } = useCurrentUser();

  // Handle user API error with retry mechanism
  React.useEffect(() => {
    if (userError && !nextRetryTime) {
      // Log detailed error information
      console.error('User API error detected:', {
        errorMessage: userError.message,
        errorStack: userError.stack,
        hasUser: !!dbUser,
        isLoading: userLoading,
        retryAttempt,
      });

      // Calculate backoff time in milliseconds
      const backoffTimeSeconds = getBackoffTime(retryAttempt);
      const backoffTimeMs = backoffTimeSeconds * 1000;

      // Set exact retry time
      const exactRetryTime = Date.now() + backoffTimeMs;
      setNextRetryTime(exactRetryTime);

      // Set initial countdown
      setRetryCountdown(backoffTimeSeconds);

      console.log(`Scheduling retry... (Attempt ${retryAttempt + 1}, waiting ${backoffTimeSeconds}s)`);
      console.log(`Next retry at: ${new Date(exactRetryTime).toLocaleTimeString()}`);

      // We'll let the countdown effect handle the retry
      // This is just a fallback in case the countdown effect fails
      const retryTimer = setTimeout(() => {
        logger.debug(`Fallback retry timer executed (Attempt ${retryAttempt + 1})`);

        // Only retry if we haven't already done so from the countdown effect
        if (nextRetryTime) {
          // Increment retry attempt for next time
          setRetryAttempt((prev) => prev + 1);

          // Reset countdown and next retry time
          setRetryCountdown(null);
          setNextRetryTime(null);

          // Force a new request to the API with force=true
          contextFetchUser(true)
            .then(() => {
              logger.debug('Fallback forced user data refresh completed');
            })
            .catch((err) => {
              console.error('Error during fallback forced refresh:', err);
            });
        }
      }, backoffTimeMs + 500); // Add a small buffer to ensure countdown effect runs first

      return () => clearTimeout(retryTimer);
    }
  }, [userError, nextRetryTime, retryAttempt, getBackoffTime, dbUser, userLoading, contextFetchUser]);

  // Countdown timer effect with immediate retry when reaching 0
  React.useEffect(() => {
    if (retryCountdown === null || !nextRetryTime) return;

    // Update the countdown every second
    const updateCountdown = () => {
      const now = Date.now();
      const remainingMs = Math.max(0, nextRetryTime - now);
      const remainingSeconds = Math.ceil(remainingMs / 1000);

      if (remainingSeconds <= 0) {
        // When countdown reaches 0, immediately retry
        if (retryCountdown !== 0) {
          setRetryCountdown(0);

          // Small delay to show the 0 before retrying
          setTimeout(() => {
            logger.debug('Countdown reached 0, forcing immediate retry');

            // Increment retry attempt for next time
            setRetryAttempt((prev) => prev + 1);

            // Reset countdown and next retry time
            setRetryCountdown(null);
            setNextRetryTime(null);

            // Force a new request to the API with force=true
            contextFetchUser(true)
              .then(() => {
                logger.debug('Forced user data refresh completed');
              })
              .catch((err) => {
                console.error('Error during forced refresh:', err);
              });
          }, 100);
        }
        return;
      }

      setRetryCountdown(remainingSeconds);
    };

    // Initial update
    updateCountdown();

    // Update countdown every second
    const timer = setInterval(updateCountdown, 1000);
    return () => clearInterval(timer);
  }, [nextRetryTime, retryCountdown, setRetryAttempt, contextFetchUser]);
  React.useEffect(() => {
    // Wait for both auth and user data to load, and ensure cache is not being refreshed
    if (authLoading || userLoading || userRefreshing) {
      logger.debug('OnboardingGuard: Still loading or refreshing', {
        authLoading,
        userLoading,
        userRefreshing,
      });

      return;
    }

    if (!authUser) {
      logger.debug('OnboardingGuard: User not authenticated, redirecting to sign in');
      router.replace(paths.auth.signIn);
      return;
    }

    if (userError) {
      logger.debug('OnboardingGuard: User API error, not proceeding', { error: String(userError) });
      return;
    }

    if (!dbUser) {
      logger.debug('OnboardingGuard: No user data yet, waiting...');
      return;
    }

    const onboardingJustCompleted =
      typeof window !== 'undefined' ? sessionStorage.getItem('onboarding_just_completed') === 'true' : false;

    logger.debug('OnboardingGuard: Checking onboarding status', {
      dbUserOnboarding: dbUser.onboarding,
      onboardingJustCompleted,
      guardType: type,
    });

    setTimeout(() => {
      logger.debug('OnboardingGuard: Making onboarding decision', {
        type,
        dbUser: dbUser ? { id: dbUser.id, onboarding: dbUser.onboarding, email: dbUser.email } : null,
        userError: userError ? String(userError) : null,
        onboardingJustCompleted,
      });

      if (type === 'onboarding') {
        if (onboardingJustCompleted) {
          logger.debug('OnboardingGuard: Onboarding just completed, allowing access despite cache state');

          if (typeof window !== 'undefined') {
            sessionStorage.removeItem('onboarding_just_completed');
          }
        } else if (dbUser.onboarding) {
          logger.debug('OnboardingGuard: User needs onboarding (onboarding = true), redirecting to onboarding');
          router.replace(paths.onboarding);
          return;
        } else {
          logger.debug('OnboardingGuard: User has completed onboarding (onboarding = false), allowing access');
        }
      } else if (type === 'onboardingRequired') {
        if (!dbUser.onboarding || onboardingJustCompleted) {
          logger.debug(
            'OnboardingGuard: User has completed onboarding or just completed, redirecting to workspace selection'
          );

          if (onboardingJustCompleted && typeof window !== 'undefined') {
            sessionStorage.removeItem('onboarding_just_completed');
          }

          router.replace(paths.workspaceSelection);
          return;
        } else {
          logger.debug('OnboardingGuard: User needs onboarding (onboarding = true), allowing access');
        }
      }

      logger.debug('OnboardingGuard: Clearing checking state, rendering children');
      setIsChecking(false);
    }, 200);
  }, [authLoading, userLoading, userRefreshing, authUser, dbUser, userError, router, type]);

  const getErrorMessage = React.useCallback(() => {
    return type === 'onboarding' ? t('loadUserError') : t('checkOnboardingError');
  }, [type, t]);

  // Create the error toast component
  const ErrorToast = userError ? (
    <Snackbar
      open={true}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      sx={{ mb: 2, mr: 2 }}
      autoHideDuration={null} // Make it persistent
    >
      <Alert
        severity='error'
        sx={{
          width: '100%',
          boxShadow: (theme) => theme.shadows[6],
          minWidth: '300px',
        }}
      >
        <Typography variant='subtitle2' fontWeight='bold' sx={{ mb: 1 }}>
          {getErrorMessage()}
        </Typography>
        <Typography variant='body2' display='block' sx={{ mt: 0.5 }}>
          {t('retryingIn', { seconds: retryCountdown?.toString() || '0' })}
        </Typography>
        <Typography variant='caption' color='text.secondary' display='block' sx={{ mt: 0.5 }}>
          {t('retryAttemptSimple', { attempt: (retryAttempt + 1).toString() })}
        </Typography>
      </Alert>
    </Snackbar>
  ) : null;

  // Show loading spinner until both user data is loaded AND onboarding check is complete
  // This prevents the app from showing before we know if user needs onboarding
  if (authLoading || userLoading || userRefreshing || isChecking) {
    return (
      <>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
          }}
        >
          <CircularProgress />
        </Box>
        {ErrorToast}
      </>
    );
  }

  // Render children with error toast if there's an error
  return (
    <>
      {children}
      {ErrorToast}
    </>
  );
}
