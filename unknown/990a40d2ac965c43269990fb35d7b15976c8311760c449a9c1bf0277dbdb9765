import { eventProcessorRegistry } from '@/lib/events/processors/registry';
import { GitPushEvent } from '@/lib/events/types/git';
import { logger } from '@/lib/logger/default-logger';
import { createIntegrationProfile } from '@/lib/profile/integration-profile-creator';
import { db } from '@/services/db';
import { upsertRepository } from './git-repository-processor';

// Register the push event processor
eventProcessorRegistry.register(GitPushEvent, async (event) => {
  logger.info(`Processing push event`, {
    eventId: event.id,
    repositoryName: event.repository.name,
    branch: event.branch,
  });

  const workspaceId = event.workspaceId;
  if (!workspaceId) {
    throw new Error('Workspace ID is required for push event processing');
  }

  const integrationId = event.integrationId;
  if (!integrationId) {
    throw new Error('Integration ID is required for push event processing');
  }

  if (!event.author) {
    throw new Error('Author is required for push events');
  }

  try {
    const repository = await upsertRepository({
      workspaceId,
      integrationId,
      repository: event.repository,
    });

    // Find or create the author
    const author = await createIntegrationProfile(event.author);

    // Process each commit in the push
    for (const commitEvent of event.commits) {
      if (!commitEvent.id) {
        logger.warn(`Commit ID is missing for commit in push event, skipping commit`, {
          eventId: event.id,
          repositoryName: event.repository.name,
        });

        continue;
      }

      const commit = commitEvent.commit;

      if (!commit) {
        logger.warn(`Commit data is missing for commit in push event, skipping commit`, {
          eventId: event.id,
          commitEvent: commitEvent.id,
          repositoryName: event.repository.name,
        });

        continue;
      }

      if (!commit.committedAt) {
        logger.warn(`Commit committedAt is missing for commit in push event, skipping commit`, {
          eventId: event.id,
          commitEvent: commitEvent.id,
          repositoryName: event.repository.name,
        });

        continue;
      }

      if (!commitEvent.committer) {
        logger.warn(`Commit committer is missing for commit in push event, skipping commit`, {
          eventId: event.id,
          commitEvent: commitEvent.id,
          repositoryName: event.repository.name,
        });

        continue;
      }

      if (!commitEvent.author) {
        logger.warn(`Commit author is missing for commit in push event, skipping commit`, {
          eventId: event.id,
          commitEvent: commitEvent.id,
          repositoryName: event.repository.name,
        });

        continue;
      }

      const committer = await createIntegrationProfile(commitEvent.committer);
      const commitAuthor = await createIntegrationProfile(commitEvent.author);

      await db.gitCommit.upsert({
        where: {
          unique: {
            sha: commitEvent.id,
            repositoryId: repository.id,
            branch: event.branch,
          },
        },
        update: {
          sha: commitEvent.id,
          message: commitEvent.commit?.message || undefined,
          authorId: commitAuthor.id,
          commiterId: committer.id,
          pusherId: author.id,
          branch: event.branch,
          repositoryId: repository.id,
          modifiedFiles: commit.modifiedFiles || undefined,
          addedFiles: commit.addedFiles || undefined,
          deletedFiles: commit.deletedFiles || undefined,
          committedAt: commit.committedAt,
          url: commit.url || undefined,
        },
        create: {
          sha: commitEvent.id,
          message: commitEvent.commit?.message || '',
          authorId: commitAuthor.id,
          commiterId: committer.id,
          pusherId: author.id,
          branch: event.branch,
          repositoryId: repository.id,
          modifiedFiles: commit.modifiedFiles || [],
          addedFiles: commit.addedFiles || [],
          deletedFiles: commit.deletedFiles || [],
          committedAt: commit.committedAt,
          url: commit.url || '',
        },
      });
    }

    logger.info(`Successfully processed push event`, {
      eventId: event.id,
      repositoryName: event.repository.name,
      branch: event.branch,
      workspaceId: workspaceId,
      integrationId: integrationId,
    });
  } catch (error) {
    // We don't throw the error further to prevent event processing from failing completely
    logger.error(
      `Error processing push event`,
      {
        eventId: event.id,
        repositoryName: event.repository.name,
        branch: event.branch,
        workspaceId: workspaceId,
        integrationId: integrationId,
      },
      error
    );
  }
});
