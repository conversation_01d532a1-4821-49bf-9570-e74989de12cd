'use client';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Stack from '@mui/material/Stack';
import type { SxProps } from '@mui/material/styles';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import type { Icon } from '@phosphor-icons/react/dist/lib/types';
import { FileCssIcon } from '@phosphor-icons/react/dist/ssr/FileCss';
import { FileJsIcon } from '@phosphor-icons/react/dist/ssr/FileJs';
import { FileTsIcon } from '@phosphor-icons/react/dist/ssr/FileTs';
import type { ApexOptions } from 'apexcharts';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

const iconMapping = {
  JavaScript: FileJsIcon,
  TypeScript: FileTsIcon,
  CSS: FileCssIcon,
} as Record<string, Icon>;

export interface CodeDistributionProps {
  chartSeries: number[];
  labels: string[];
  sx?: SxProps;
}

export function Traffic({ chartSeries, labels, sx }: CodeDistributionProps): React.JSX.Element {
  const chartOptions = useChartOptions(labels);

  return (
    <Card sx={sx}>
      <CardHeader title='Code Distribution' />
      <CardContent>
        <Stack spacing={2}>
          <Chart height={300} options={chartOptions} series={chartSeries} type='donut' width='100%' />
          <Stack direction='row' spacing={2} sx={{ alignItems: 'center', justifyContent: 'center' }}>
            {chartSeries.map((item, index) => {
              const label = labels[index];
              const Icon = iconMapping[label];

              return (
                <Stack key={label} spacing={1} sx={{ alignItems: 'center' }}>
                  {Icon ? <Icon fontSize='var(--icon-fontSize-lg)' /> : null}
                  <Typography variant='h6'>{label}</Typography>
                  <Typography color='text.secondary' variant='subtitle2'>
                    {item}%
                  </Typography>
                </Stack>
              );
            })}
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
}

function useChartOptions(labels: string[]): ApexOptions {
  const theme = useTheme();

  return {
    chart: { background: 'transparent' },
    colors: [theme.palette.primary.main, theme.palette.success.main, theme.palette.warning.main],
    dataLabels: { enabled: false },
    labels,
    legend: { show: false },
    plotOptions: { pie: { expandOnClick: false } },
    states: {
      active: { filter: { type: 'none' } },
      hover: { filter: { type: 'none' } },
    },
    stroke: { width: 0 },
    theme: { mode: theme.palette.mode },
    tooltip: { fillSeriesColor: false },
  };
}
