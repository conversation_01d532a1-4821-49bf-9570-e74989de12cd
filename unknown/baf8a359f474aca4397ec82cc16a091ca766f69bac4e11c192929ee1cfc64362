'use client';

import { integrationsRegistry } from '@/services/integrations';
import { IntegrationHandler } from '@/services/integrations/integration';
import { useColorScheme } from '@mui/material';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import Divider from '@mui/material/Divider';
import Skeleton from '@mui/material/Skeleton';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { ArrowLeftIcon } from '@phosphor-icons/react/dist/ssr/ArrowLeft';
import { PlugsConnectedIcon } from '@phosphor-icons/react/dist/ssr/PlugsConnected';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { notFound, useSearchParams } from 'next/navigation';
import * as React from 'react';

import { useWorkspace } from '@/contexts/workspace-context';
import { useApiServices } from '@/hooks/use-api-services';
import { WorkspaceIntegrationWithChannels } from '@/lib/models/integration';
import { paths } from '@/paths';
import { IntegrationChannel, IntegrationStatus } from '@prisma/client';

export function IntegrationDetailClient(): React.JSX.Element {
  const searchParams = useSearchParams();
  const id = searchParams.get('id');

  // Get API services and workspace context
  const { integrationApiService } = useApiServices();
  const { currentWorkspace } = useWorkspace();

  // State for integration details
  const [workspaceIntegrations, setWorkspaceIntegrations] = React.useState<
    WorkspaceIntegrationWithChannels[] | undefined
  >();
  const [isLoadingDetails, setIsLoadingDetails] = React.useState<boolean>(false);
  const [detailsError, setDetailsError] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState<boolean>(false);

  // Get the integration handler from the registry
  const handler: IntegrationHandler | undefined = id ? integrationsRegistry.getHandler(id) : undefined;

  // If handler not found, show 404
  if (!handler) {
    notFound();
  }

  // Get the integration data
  const integration = handler.getIntegration();

  const t = useTranslations('settings.integrations');

  // Initially use the integration status from the handler
  const [isInstalled, setIsInstalled] = React.useState(integration.status === 'installed');
  const isComingSoon = integration.status === 'coming-soon';
  const handleInstall = React.useCallback(async () => {
    // Check if we have a current workspace
    if (!currentWorkspace) {
      console.error('No workspace selected');
      return;
    }

    setIsLoading(true);
    try {
      // Call the handler's install method with required props
      await handler.onInstallAction({
        workspaceId: currentWorkspace.id,
        integrationApiService,
        setIsLoading,
      });
    } catch (error) {
      console.error('Failed to install integration', error);
      setIsLoading(false);
    }
  }, [handler, currentWorkspace, integrationApiService]);

  // Fetch integration details when the component mounts or when the workspace changes
  const fetchIntegrationDetails = React.useCallback(async () => {
    if (!currentWorkspace || !id || isComingSoon) {
      return;
    }

    setIsLoadingDetails(true);
    setDetailsError(null);
    try {
      const backendIntegrations = await integrationApiService.get(currentWorkspace.id, [id as IntegrationChannel]);
      setWorkspaceIntegrations(backendIntegrations);

      const installed = backendIntegrations.some((backendIntegration) => {
        return backendIntegration.status !== IntegrationStatus.Uninstalled;
      });

      // Update the installed state based on the API response
      if (backendIntegrations && installed) {
        setIsInstalled(true);
      } else {
        setIsInstalled(false);
      }
    } catch (error) {
      console.error('Failed to fetch integration details', error);

      // Check if error is 404 - this means the integration is not installed
      if ((error as { status?: number })?.status === 404) {
        // 404 means not installed, not an error to show to user
        setIsInstalled(false);
        setWorkspaceIntegrations(undefined);
      } else {
        // For other errors, show error message to user
        setDetailsError('Failed to load integration details');
      }
    } finally {
      setIsLoadingDetails(false);
    }
  }, [currentWorkspace, id, integrationApiService, isComingSoon]);

  // Fetch integration details when the component mounts
  React.useEffect(() => {
    fetchIntegrationDetails();
  }, [fetchIntegrationDetails]);

  const { colorScheme } = useColorScheme();
  const useDarkLogo = colorScheme === 'dark' && integration.darkLogo;
  const logoPath = `/assets/logos/${integration.id}${useDarkLogo ? '-dark' : ''}.svg`;

  return (
    <Stack spacing={3}>
      <Stack direction='row' spacing={2} alignItems='center'>
        <Button
          component={Link}
          href={paths.settings.integrations.index}
          startIcon={<ArrowLeftIcon fontSize='var(--icon-fontSize-md)' />}
          variant='text'
        >
          {t('backToIntegrations')}
        </Button>
      </Stack>

      <Card>
        <CardContent>
          <Stack spacing={3}>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} alignItems='center'>
              <Avatar src={logoPath} variant='square' sx={{ width: 64, height: 64 }} />
              <Stack spacing={1} sx={{ flex: 1 }}>
                <Stack direction='row' spacing={2} alignItems='center'>
                  <Typography variant='h4'>{integration.title}</Typography>
                  {isComingSoon && <Chip label={t('status.comingSoon')} color='primary' size='small' />}
                  {isLoadingDetails && (
                    <Skeleton variant='rectangular' width={80} height={24} sx={{ borderRadius: 1 }} />
                  )}
                  {!isLoadingDetails && isInstalled && (
                    <Chip
                      label={t('status.installed')}
                      color='success'
                      size='small'
                      clickable={false}
                      onClick={() => {}}
                    />
                  )}
                </Stack>
                <Typography variant='body1'>{t(`descriptions.${integration.id.toLowerCase()}` as any)}</Typography>
              </Stack>
              {!isComingSoon && !isInstalled && !isLoadingDetails && (
                <Button
                  variant='contained'
                  color='primary'
                  startIcon={<PlugsConnectedIcon fontSize='var(--icon-fontSize-md)' />}
                  onClick={handleInstall}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <CircularProgress size={20} sx={{ mr: 1 }} />
                      {t('actions.installing')}
                    </>
                  ) : (
                    t('actions.install')
                  )}
                </Button>
              )}
            </Stack>

            <Divider />

            <Stack spacing={2}>
              <Typography variant='h6'>{t('capabilitiesTitle')}</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {integration.capabilities.map((capability: string) => (
                  <Chip
                    key={capability}
                    label={t(`capabilities.${capability}` as any)}
                    size='small'
                    clickable={false}
                    onClick={() => {}}
                  />
                ))}
              </Box>
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {/* Show skeleton while loading */}
      {!isComingSoon && isLoadingDetails && (
        <Card>
          <CardContent>
            <Stack spacing={2}>
              <Skeleton variant='text' width='30%' height={32} />
              <Skeleton variant='text' width='90%' height={20} />
              <Skeleton variant='text' width='80%' height={20} />
              <Skeleton variant='rectangular' width='100%' height={120} sx={{ borderRadius: 1 }} />
            </Stack>
          </CardContent>
        </Card>
      )}

      {/* Always show the documentation component if available, regardless of installation status */}
      {!isComingSoon &&
        handler.DocComponent &&
        !isLoadingDetails &&
        React.createElement(handler.DocComponent, {
          workspaceId: currentWorkspace?.id || '',
          workspaceIntegrations: workspaceIntegrations,
          integrationHandler: handler,
          integration: integration,
        })}

      {/* Only show the configuration component if the integration is installed */}
      {isInstalled &&
        !isLoadingDetails &&
        handler.ConfigComponent &&
        React.createElement(handler.ConfigComponent, {
          workspaceId: currentWorkspace?.id || '',
          workspaceIntegrations: workspaceIntegrations,
          integrationHandler: handler,
          integration: integration,
        })}

      {isInstalled && isLoadingDetails && (
        <Card>
          <CardContent>
            <Stack spacing={2}>
              <Skeleton variant='text' width='40%' height={32} />
              <Skeleton variant='rectangular' width='100%' height={200} />
            </Stack>
          </CardContent>
        </Card>
      )}

      {detailsError && (
        <Card>
          <CardContent>
            <Stack spacing={2} alignItems='center' sx={{ py: 3 }}>
              <Typography variant='h6' color='error' align='center'>
                {t('errorState.title')}
              </Typography>
              <Typography variant='body2' color='text.secondary' align='center'>
                {detailsError}
              </Typography>
              <Button variant='contained' onClick={fetchIntegrationDetails}>
                {t('errorState.retry')}
              </Button>
            </Stack>
          </CardContent>
        </Card>
      )}

      {isComingSoon && (
        <Card>
          <CardContent>
            <Stack spacing={2} alignItems='center' sx={{ py: 3 }}>
              <Typography variant='h6' align='center'>
                {t('comingSoonMessage')}
              </Typography>
              <Typography variant='body2' color='text.secondary' align='center'>
                {t('notifyMessage')}
              </Typography>
              <Button variant='contained'>{t('notifyButton')}</Button>
            </Stack>
          </CardContent>
        </Card>
      )}
    </Stack>
  );
}
