import 'server-only';

import { NextRequest, NextResponse } from 'next/server';
/**
 * @swagger
 * /api/workspace/selected:
 *   get:
 *     tags:
 *       - Workspace
 *     summary: Get selected workspace
 *     description: Get the currently selected workspace
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Selected workspace
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Workspace'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: No workspace selected
 *       500:
 *         description: Server error
 */

import { deleteCookie, getCookie } from 'cookies-next';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * GET /api/workspace/selected
 * Get the currently selected workspace for the current user
 */
export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Get the active workspace ID from cookies
    const activeWorkspaceId = await getCookie('selected-workspace', { req: request });

    if (!activeWorkspaceId) {
      return NextResponse.json({ error: 'No workspace selected' }, { status: 404 });
    }

    // Get the workspace if the user is a member
    const workspace = await db.workspace.findFirst({
      where: {
        id: activeWorkspaceId.toString(),
        members: {
          some: {
            userId: currentUser.uid,
          },
        },
      },
      include: {
        members: {
          where: {
            userId: currentUser.uid,
          },
          include: {
            role: true,
          },
        },
      },
    });

    if (!workspace) {
      // If the workspace doesn't exist or the user is not a member, clear the cookie
      const response = NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
      deleteCookie('selected-workspace', { req: request, res: response });
      return response;
    }

    return NextResponse.json(workspace);
  } catch (error) {
    console.error('Error getting selected workspace:', error);
    return NextResponse.json({ error: 'Failed to get selected workspace' }, { status: 500 });
  }
}
