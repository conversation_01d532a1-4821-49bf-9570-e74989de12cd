import {
  Integration,
  IntegrationHandler,
  IntegrationInstallProps,
  IntegrationUninstallProps,
} from '@/services/integrations/integration';
import { IntegrationChannel } from '@prisma/client';

// Bitbucket integration data
const bitbucketIntegration: Integration = {
  id: IntegrationChannel.Bitbucket,
  title: 'Bitbucket',
  status: 'coming-soon',
  capabilities: ['pullRequests', 'codeReview', 'commit', 'repositoryAnalytics', 'cicdPipelines'],
};

// Bitbucket integration handler
export const bitbucketHandler: IntegrationHandler = {
  // Return the integration data
  getIntegration: () => {
    return bitbucketIntegration;
  },

  // Handle installation (not implemented for coming soon)
  onInstallAction: async (_props: IntegrationInstallProps) => {
    console.log('Installing Bitbucket integration');
    // Not implemented for coming soon
    return false;
  },

  // Handle uninstallation (not implemented for coming soon)
  onUninstallAction: async (_props: IntegrationUninstallProps) => {
    console.log('Uninstalling Bitbucket integration');
    // Not implemented for coming soon
    return false;
  },

  // This integration doesn't have a custom install flow
  hasCustomInstallFlow: () => false,
};
