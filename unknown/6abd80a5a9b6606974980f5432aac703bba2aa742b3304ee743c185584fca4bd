'use client';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { MedalIcon } from '@phosphor-icons/react/dist/ssr/Medal';
import { TrophyIcon } from '@phosphor-icons/react/dist/ssr/Trophy';
import type { ApexOptions } from 'apexcharts';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

export function BenchmarksComparison(): React.JSX.Element {
  const t = useTranslations('insights');
  const theme = useTheme();

  // Sample data for market comparison
  const marketComparisonData = {
    categories: ['Cycle Time', 'Lead Time', 'Deploy Frequency', 'MTTR'],
    yourTeam: [6.5, 8.5, 5, 3.5],
    market25th: [8, 10, 3, 5],
    market50th: [5, 7, 7, 3],
    market75th: [3, 4, 10, 1.5],
  };

  // Sample data for internal team comparison
  const internalComparisonData = {
    teams: ['Frontend', 'Backend', 'DevOps', 'Mobile', 'QA'],
    scores: [85, 92, 78, 65, 70],
  };

  // Chart options for market comparison chart
  const marketComparisonChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [
      theme.palette.primary.main,
      theme.palette.success.light,
      theme.palette.warning.main,
      theme.palette.error.light,
    ],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    legend: { show: true, position: 'top' },
    stroke: { curve: 'smooth', width: 3 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: marketComparisonData.categories,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => `${value}`,
        style: { colors: theme.palette.text.secondary },
      },
    },
  };

  // Chart options for internal team comparison chart
  const internalComparisonChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [theme.palette.primary.main, theme.palette.primary.light, theme.palette.primary.dark],
    dataLabels: {
      enabled: true,
      formatter: (val) => `${String(val)}`,
      style: {
        fontSize: '14px',
        fontWeight: 500,
        colors: [theme.palette.common.white],
      },
    },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
        borderRadius: 4,
      },
    },
    stroke: { width: 0 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: internalComparisonData.teams,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      max: 100,
      labels: {
        formatter: (value) => `${value}`,
        style: { colors: theme.palette.text.secondary },
      },
    },
  };

  return (
    <Grid container spacing={3}>
      {/* Market Comparison Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.marketComparison')} subheader={t('metrics.anonymisedBenchmark')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={300}
                options={marketComparisonChartOptions}
                series={[
                  { name: 'Your Team', data: marketComparisonData.yourTeam },
                  { name: '25th Percentile', data: marketComparisonData.market25th },
                  { name: '50th Percentile', data: marketComparisonData.market50th },
                  { name: '75th Percentile', data: marketComparisonData.market75th },
                ]}
                type='radar'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <TrophyIcon fontSize='var(--icon-fontSize-lg)' color={theme.palette.warning.main} />
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2'>Your team is between 50th-75th percentile overall</Typography>
                  <Typography variant='caption' color='text.secondary'>
                    Cycle Time and Lead Time are better than average, but Deploy Frequency is below average
                  </Typography>
                </Stack>
              </Stack>

              <Typography variant='body2' color='primary'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.shareBestPractices')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* Internal Team Comparison Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.internalBenchmark')} subheader={t('metrics.internalHealthScore')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={300}
                options={internalComparisonChartOptions}
                series={[{ name: 'Health Score', data: internalComparisonData.scores }]}
                type='bar'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <MedalIcon fontSize='var(--icon-fontSize-lg)' color={theme.palette.success.main} />
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2'>Backend team has the highest health score (92/100)</Typography>
                  <Typography variant='caption' color='text.secondary'>
                    Mobile team has the lowest score and needs improvement
                  </Typography>
                </Stack>
              </Stack>

              <Typography variant='body2' color='primary'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.createGuilds')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}
