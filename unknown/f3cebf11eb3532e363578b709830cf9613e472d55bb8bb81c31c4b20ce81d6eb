/**
 * Unified timezone configuration with IANA timezone names
 * This file provides a standardized list of timezones to be used across
 * the application
 */

export interface Timezone {
  /** IANA timezone identifier */
  value: string;
  /** User-friendly label with UTC offset and location */
  label: string;
}

/**
 * Comprehensive list of supported timezones using IANA timezone names
 * Organized by region for better user experience
 */
const TIMEZONES = [
  // Pacific
  {
    value: 'Pacific/Midway',
    label: '(UTC-11:00) Midway Island, Samoa',
  },
  {
    value: 'Pacific/Honolulu',
    label: '(UTC-10:00) Hawaii',
  },
  {
    value: 'America/Anchorage',
    label: '(UTC-09:00) Alaska',
  },

  // Americas - North America
  {
    value: 'America/Los_Angeles',
    label: '(UTC-08:00) Pacific Time (US & Canada)',
  },
  {
    value: 'America/Denver',
    label: '(UTC-07:00) Mountain Time (US & Canada)',
  },
  {
    value: 'America/Chicago',
    label: '(UTC-06:00) Central Time (US & Canada)',
  },
  {
    value: 'America/New_York',
    label: '(UTC-05:00) Eastern Time (US & Canada)',
  },
  {
    value: 'America/Halifax',
    label: '(UTC-04:00) Atlantic Time (Canada)',
  },

  // Americas - South America
  {
    value: 'America/Sao_Paulo',
    label: '(UTC-03:00) Brasilia, São Paulo',
  },
  {
    value: 'America/Argentina/Buenos_Aires',
    label: '(UTC-03:00) Buenos Aires, Georgetown',
  },
  {
    value: 'America/Godthab',
    label: '(UTC-02:00) Greenland',
  },
  {
    value: 'Atlantic/Azores',
    label: '(UTC-01:00) Azores',
  },

  // Europe & Africa
  {
    value: 'Europe/London',
    label: '(UTC+00:00) London, Dublin, Lisbon',
  },
  {
    value: 'Europe/Paris',
    label: '(UTC+01:00) Berlin, Paris, Rome, Madrid',
  },
  {
    value: 'Europe/Athens',
    label: '(UTC+02:00) Athens, Istanbul, Helsinki',
  },
  {
    value: 'Europe/Moscow',
    label: '(UTC+03:00) Moscow, St. Petersburg',
  },

  // Middle East & Asia
  {
    value: 'Asia/Dubai',
    label: '(UTC+04:00) Dubai, Abu Dhabi',
  },
  {
    value: 'Asia/Karachi',
    label: '(UTC+05:00) Islamabad, Karachi',
  },
  {
    value: 'Asia/Kolkata',
    label: '(UTC+05:30) New Delhi, Mumbai',
  },
  {
    value: 'Asia/Dhaka',
    label: '(UTC+06:00) Dhaka, Almaty',
  },
  {
    value: 'Asia/Bangkok',
    label: '(UTC+07:00) Bangkok, Jakarta',
  },
  {
    value: 'Asia/Singapore',
    label: '(UTC+08:00) Beijing, Singapore, Hong Kong',
  },
  {
    value: 'Asia/Tokyo',
    label: '(UTC+09:00) Tokyo, Seoul',
  },
  {
    value: 'Australia/Sydney',
    label: '(UTC+10:00) Sydney, Melbourne',
  },
  {
    value: 'Asia/Vladivostok',
    label: '(UTC+11:00) Vladivostok',
  },
  {
    value: 'Pacific/Auckland',
    label: '(UTC+12:00) Auckland, Wellington',
  },
] as const;

/**
 * Get timezone by IANA key
 */
export function getTimezoneByKey(value: string): Timezone | undefined {
  return TIMEZONES.find((tz) => tz.value === value);
}

/**
 * Get the default timezone (São Paulo/Brasilia)
 */
export function getDefaultTimezone(): Timezone {
  return getTimezoneByKey('America/Sao_Paulo')!;
}

/**
 * Check if a timezone key is valid
 */
export function isValidTimezone(value: string): boolean {
  return getTimezoneByKey(value) !== undefined;
}

/**
 * Get timezone options for form components
 * Returns the immutable timezone array for optimal performance
 */
export function getTimezoneOptions(): readonly Timezone[] {
  return TIMEZONES;
}
