import { User } from '@prisma/client';

import { CoreApiService } from './core';

/**
 * User API service
 * This service provides methods for interacting with the user API
 */
export class UserApiService {
  private api: CoreApiService;

  /**
   * Create a new UserApiService
   * @param apiService The API service to use
   */
  constructor(apiService: CoreApiService) {
    this.api = apiService;
  }

  /**
   * Get the current user or create a new one if they don't exist
   * @returns A promise that resolves to the user data
   */
  async getCurrentUser(): Promise<User> {
    return await this.api.request({
      method: 'GET',
      url: '/user',
    });
  }

  /**
   * Update the current user's profile information
   * @param user The user data to update, only the provided fields will be updated
   * @returns A promise that resolves to the updated user data
   */
  async updateCurrentUser(user: Partial<User>): Promise<User> {
    return await this.api.request({
      method: 'PATCH',
      url: '/user',
      data: user,
    });
  }

  async createAvatarPresignedUrl(
    contentType: string,
    contentLength: number
  ): Promise<{ url: string; expiration: string; path: string; cacheControl: string }> {
    return await this.api.request({
      method: 'POST',
      url: `/user/avatar?content-type=${contentType}&content-length=${contentLength}`,
    });
  }

  /**
   * Complete the onboarding process
   * @param onboardingData The onboarding data
   * @returns A promise that resolves to the updated user data
   */
  async completeOnboarding(onboardingData: {
    displayName: string;
    country?: string;
    timezone?: string;
    phone?: string;
    language?: string;
    theme?: 'light' | 'dark' | 'system';
  }): Promise<User> {
    return await this.api.request({
      method: 'PATCH',
      url: '/user/onboarding',
      data: onboardingData,
    });
  }
}

/**
 * Create a user API service with the given API service
 * @param apiService The API service to use
 * @returns A new user API service
 */
export function createUserApiService(apiService: CoreApiService): UserApiService {
  return new UserApiService(apiService);
}
