import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { TeamHealth } from '@/components/insights/team-health';
import { config } from '@/config';

export const metadata = {
  title: `Team Health | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('insights');

  return (
    <Stack spacing={3}>
      <Typography variant='h4'>{t('categories.teamHealth')}</Typography>
      <Typography color='text.secondary' variant='body1'>
        Monitoring team wellbeing and workload balance to prevent burnout and optimize productivity.
      </Typography>

      <TeamHealth />
    </Stack>
  );
}
