'use client';

import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import LinearProgress from '@mui/material/LinearProgress';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { GitPullRequestIcon } from '@phosphor-icons/react/dist/ssr/GitPullRequest';
import { HourglassIcon } from '@phosphor-icons/react/dist/ssr/Hourglass';
import type { ApexOptions } from 'apexcharts';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

export function DeliveryDeadlines(): React.JSX.Element {
  const t = useTranslations('insights');
  const theme = useTheme();

  // Sample data for burn-down chart
  const burndownData = {
    ideal: [100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 0],
    actual: [100, 95, 88, 82, 75, 70, 65, 60, 55, 50, 45],
    forecast: [45, 38, 30, 22, 15, 8, 0],
  };

  // Sample data for PRs blocking deploy
  const blockingPRs = [
    { id: 'PR-123', title: 'Fix authentication bug', hours: 24, reviewer: 'John Doe' },
    { id: 'PR-124', title: 'Add new dashboard features', hours: 12, reviewer: 'Jane Smith' },
    { id: 'PR-125', title: 'Update API endpoints', hours: 8, reviewer: 'Bob Johnson' },
  ];

  // Sample data for cycle time by team
  const cycleTimeData = [
    { team: 'Frontend', coding: 2.5, review: 1.8, merge: 0.7 },
    { team: 'Backend', coding: 3.2, review: 2.1, merge: 0.5 },
    { team: 'DevOps', coding: 1.8, review: 1.2, merge: 0.3 },
  ];

  // Chart options for burn-down chart
  const burndownChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [theme.palette.primary.main, theme.palette.success.main, theme.palette.warning.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    legend: { show: true, position: 'top' },
    stroke: { curve: 'straight', width: 2 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: [
        'Day 1',
        'Day 2',
        'Day 3',
        'Day 4',
        'Day 5',
        'Day 6',
        'Day 7',
        'Day 8',
        'Day 9',
        'Day 10',
        'Day 11',
        'Day 12',
        'Day 13',
        'Day 14',
        'Day 15',
      ],
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => `${value}%`,
        style: { colors: theme.palette.text.secondary },
      },
    },
  };

  // Chart options for cycle time chart
  const cycleTimeChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      stacked: true,
      toolbar: { show: false },
      type: 'bar',
    },
    colors: [theme.palette.primary.main, theme.palette.success.main, theme.palette.warning.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    legend: { show: true, position: 'top' },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: '70%',
      },
    },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: cycleTimeData.map((item) => item.team),
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => `${value} days`,
        style: { colors: theme.palette.text.secondary },
      },
    },
  };

  return (
    <Grid container spacing={3}>
      {/* Sprint Deadline Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.sprintDeadline')} subheader={t('metrics.burndownForecast')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={300}
                options={burndownChartOptions}
                series={[
                  { name: 'Ideal', data: burndownData.ideal },
                  { name: 'Actual', data: burndownData.actual },
                  {
                    name: 'Forecast',
                    data: [
                      ...Array(burndownData.actual.length - burndownData.forecast.length).fill(null),
                      ...burndownData.forecast,
                    ],
                  },
                ]}
                type='line'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <HourglassIcon fontSize='var(--icon-fontSize-lg)' />
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2' color='text.secondary'>
                    {t('metrics.percentDone')}
                  </Typography>
                  <LinearProgress
                    variant='determinate'
                    value={55}
                    color='warning'
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Stack>
                <Typography variant='h6'>55%</Typography>
              </Stack>

              <Typography variant='body2' color='error'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.rescope')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* Blocking PRs Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.blockingPRs')} subheader={t('metrics.openPRs')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Stack spacing={2}>
                {blockingPRs.map((pr) => (
                  <Card key={pr.id} variant='outlined' sx={{ p: 2 }}>
                    <Stack direction='row' spacing={2} alignItems='center'>
                      <GitPullRequestIcon fontSize='var(--icon-fontSize-lg)' color={theme.palette.error.main} />
                      <Stack sx={{ flex: 1 }}>
                        <Typography variant='subtitle2'>
                          {pr.id}: {pr.title}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          Waiting for {pr.reviewer} • {pr.hours}h without review
                        </Typography>
                      </Stack>
                    </Stack>
                  </Card>
                ))}
              </Stack>

              <Typography variant='body2' color='error'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.notifyReviewers')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* Pipeline Bottleneck Question */}
      <Grid size={{ xs: 12 }}>
        <Card>
          <CardHeader title={t('questions.pipelineBottleneck')} subheader={t('metrics.cycleTime')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={250}
                options={cycleTimeChartOptions}
                series={[
                  { name: 'Coding', data: cycleTimeData.map((item) => item.coding) },
                  { name: 'Review', data: cycleTimeData.map((item) => item.review) },
                  { name: 'Merge', data: cycleTimeData.map((item) => item.merge) },
                ]}
                type='bar'
                width='100%'
              />

              <Typography variant='body2' color='error'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.enablePairReview')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}
