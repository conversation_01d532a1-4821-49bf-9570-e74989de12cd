export enum Language {
  EN_US = 'en-US',
  PT_BR = 'pt-BR',
  ES = 'es',
}

export function languageAlias(lang: Language): string {
  return lang.split('-')[0];
}

/**
 * Checks if the provided language string is a valid member of the Language enum.
 *
 * @param lang - The language string to validate.
 * @returns True if the language string is a valid Language enum member, false otherwise.
 */
export function isValidLanguage(lang: string): lang is Language {
  return Object.values(Language).includes(lang as Language);
}
