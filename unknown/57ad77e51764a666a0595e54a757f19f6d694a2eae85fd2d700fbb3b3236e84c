'use client';

import { useRouter } from '@/i18n/navigation';
import Button from '@mui/material/Button';
import { SignOutIcon } from '@phosphor-icons/react/dist/ssr/SignOut';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { useAuth } from '@/contexts/firebase-auth-context';
import { paths } from '@/paths';

export function LogoutButton(): React.JSX.Element {
  const { logout } = useAuth();
  const router = useRouter();
  const t = useTranslations('userPopup');

  const handleLogout = React.useCallback(async () => {
    try {
      // Clear any cached data
      if (typeof window !== 'undefined') {
        localStorage.clear();
        sessionStorage.clear();
      }
      await logout();
      router.push(paths.auth.signIn);
    } catch (error) {
      console.error('Logout error:', error);
    }
  }, [logout, router]);

  return (
    <Button variant='outlined' size='small' startIcon={<SignOutIcon />} onClick={handleLogout}>
      {t('signOut')}
    </Button>
  );
}
