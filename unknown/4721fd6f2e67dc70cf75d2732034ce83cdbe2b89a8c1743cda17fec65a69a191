'use client';

import Avatar from '@mui/material/Avatar';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Stack from '@mui/material/Stack';
import type { SxProps } from '@mui/material/styles';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowDownIcon } from '@phosphor-icons/react/dist/ssr/ArrowDown';
import { ArrowUpIcon } from '@phosphor-icons/react/dist/ssr/ArrowUp';
import { GitPullRequestIcon } from '@phosphor-icons/react/dist/ssr/GitPullRequest';
import type { ApexOptions } from 'apexcharts';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

export interface PullRequestsProps {
  diff?: number;
  trend: 'up' | 'down';
  sx?: SxProps;
  value: string;
  chartData?: number[];
}

export function PullRequests({ diff, trend, sx, value, chartData = [] }: PullRequestsProps): React.JSX.Element {
  const t = useTranslations('git.overview');
  const chartOptions = useChartOptions();
  const TrendIcon = trend === 'up' ? ArrowUpIcon : ArrowDownIcon;
  const trendColor = trend === 'up' ? 'var(--mui-palette-success-main)' : 'var(--mui-palette-error-main)';

  return (
    <Card sx={sx}>
      <CardContent>
        <Stack spacing={2}>
          <Stack direction='row' sx={{ alignItems: 'flex-start', justifyContent: 'space-between' }} spacing={3}>
            <Stack spacing={1}>
              <Typography color='text.secondary' variant='overline'>
                {t('metrics.pullRequests')}
              </Typography>
              <Typography variant='h4'>{value}</Typography>
              <Typography variant='caption' color='text.secondary'>
                {t('timeframes.lastDays', { days: 30 })}
              </Typography>
            </Stack>
            <Avatar
              sx={{
                backgroundColor: 'var(--mui-palette-success-main)',
                height: '56px',
                width: '56px',
              }}
            >
              <GitPullRequestIcon fontSize='var(--icon-fontSize-lg)' />
            </Avatar>
          </Stack>
          {diff ? (
            <Stack sx={{ alignItems: 'center' }} direction='row' spacing={2}>
              <Stack sx={{ alignItems: 'center' }} direction='row' spacing={0.5}>
                <TrendIcon color={trendColor} fontSize='var(--icon-fontSize-md)' />
                <Typography color={trendColor} variant='body2'>
                  {diff}%
                </Typography>
              </Stack>
              <Typography color='text.secondary' variant='caption'>
                {t('timeframes.sinceLastMonth')}
              </Typography>
            </Stack>
          ) : null}
        </Stack>
      </CardContent>

      {chartData.length > 0 && (
        <CardContent sx={{ pt: 0, pb: 1 }}>
          <Chart
            height={80}
            options={chartOptions}
            series={[{ name: 'Pull Requests', data: chartData }]}
            type='area'
            width='100%'
          />
        </CardContent>
      )}
    </Card>
  );
}

function useChartOptions(): ApexOptions {
  const theme = useTheme();

  // Generate the last 30 days as categories
  const generateLast30Days = () => {
    const days = [];
    const dates = [];
    for (let i = 29; i >= 0; i--) {
      // Format as "Jun 1", "Jun 2", etc.
      const date = new Date();
      date.setDate(date.getDate() - i);
      const day = date.getDate();
      const month = date.toLocaleString('en-US', { month: 'short' });
      days.push(`${month} ${day}`);
      dates.push(date);
    }
    return { categories: days, dates: dates };
  };

  const { categories, dates } = generateLast30Days();

  return {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      sparkline: { enabled: true },
      zoom: { enabled: false },
    },
    colors: [theme.palette.success.main],
    dataLabels: { enabled: false },
    fill: {
      opacity: 0.2,
      type: 'gradient',
      gradient: {
        shade: 'light',
        type: 'vertical',
        shadeIntensity: 0.2,
        opacityFrom: 0.7,
        opacityTo: 0,
        stops: [0, 100],
      },
    },
    grid: {
      show: false,
    },
    stroke: {
      curve: 'smooth',
      width: 2,
    },
    theme: { mode: theme.palette.mode },
    tooltip: {
      enabled: true,
      custom: function ({ seriesIndex, dataPointIndex, w }) {
        // Get the value directly from the w.globals.series array
        const value = w.globals.series[seriesIndex][dataPointIndex];
        const date = dates[dataPointIndex];
        const formattedDate = date.toLocaleDateString('en-US', {
          weekday: 'short',
          month: 'short',
          day: 'numeric',
        });

        return `<div class="apexcharts-tooltip-title" style="font-family: inherit; font-size: 12px; padding-bottom: 4px;">${formattedDate}</div>
                <div class="apexcharts-tooltip-series-group" style="padding: 4px 8px;">
                  <span class="apexcharts-tooltip-text" style="font-family: inherit; font-size: 12px;">
                    <span class="apexcharts-tooltip-text-label">Pull Requests: </span>
                    <span class="apexcharts-tooltip-text-value">${value}</span>
                  </span>
                </div>`;
      },
      marker: {
        show: false,
      },
    },
    xaxis: {
      categories: categories,
      labels: {
        show: false,
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      labels: {
        show: false,
      },
    },
  };
}
