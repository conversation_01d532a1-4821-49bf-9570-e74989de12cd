import { db } from '@/services/db';
import { IntegrationProfile } from '@prisma/client';

// TODO: SHOULD CACHE THIS

/**
 * Creates or updates an integration profile in the database.
 *
 * @param profile The integration profile data
 * @returns The created or updated integration profile
 *
 * @throws If workspaceId, channel, or idOnChannel are missing
 */
export async function createIntegrationProfile(profile: Partial<IntegrationProfile>): Promise<IntegrationProfile> {
  if (!profile.workspaceId) {
    throw new Error('Workspace ID is required');
  }

  if (!profile.channel) {
    throw new Error('Channel is required');
  }

  if (!profile.idOnChannel && !profile.username) {
    throw new Error('ID on channel or username is required');
  }

  // Make sure to not add the ID field, since it is auto-generated
  const { id: _id, ...data } = profile;

  return await db.$transaction(async (tx) => {
    let existingProfile: IntegrationProfile | null = null;

    if (profile.idOnChannel) {
      existingProfile = await tx.integrationProfile.findFirst({
        where: {
          channel: profile.channel,
          idOnChannel: profile.idOnChannel,
          workspaceId: profile.workspaceId,
        },
      });
    } else if (profile.username) {
      existingProfile = await tx.integrationProfile.findFirst({
        where: {
          channel: profile.channel,
          username: profile.username,
          workspaceId: profile.workspaceId,
        },
      });
    }

    if (!existingProfile) {
      return await tx.integrationProfile.create({
        data: data as IntegrationProfile,
      });
    }

    const emails = new Set(existingProfile?.emails ?? []);
    profile.emails?.forEach((email) => emails.add(email));

    // Check if any fields have actually changed
    const hasChanges =
      (profile.username && profile.username !== existingProfile.username) ||
      (profile.name && profile.name !== existingProfile.name) ||
      (profile.idOnChannel && profile.idOnChannel !== existingProfile.idOnChannel) ||
      emails.size !== existingProfile.emails.length;

    if (hasChanges) {
      return await tx.integrationProfile.update({
        where: { id: existingProfile.id },
        data: {
          username: profile.username || existingProfile.username,
          name: profile.name || existingProfile.name,
          idOnChannel: profile.idOnChannel || existingProfile.idOnChannel,
          emails: emails.size !== existingProfile.emails.length ? Array.from(emails) : undefined,
        },
      });
    }

    return existingProfile;
  });
}
