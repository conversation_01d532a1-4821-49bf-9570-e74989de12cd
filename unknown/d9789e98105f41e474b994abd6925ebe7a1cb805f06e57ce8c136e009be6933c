'use client';

import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import Tooltip from '@mui/material/Tooltip';
import { RowsIcon } from '@phosphor-icons/react/dist/ssr/Rows';
import { SquaresFourIcon } from '@phosphor-icons/react/dist/ssr/SquaresFour';
import { useTranslations } from 'next-intl';
import * as React from 'react';

export type ViewMode = 'card' | 'table';

interface ViewToggleProps {
  view: ViewMode;
  onChangeAction: (_: ViewMode) => void;
}

export function ViewToggle({ view, onChangeAction }: ViewToggleProps): React.JSX.Element {
  const t = useTranslations('settings.integrations');

  const handleChange = (_: React.MouseEvent<HTMLElement>, newView: ViewMode | null) => {
    if (newView !== null) {
      onChangeAction(newView);
    }
  };

  return (
    <ToggleButtonGroup
      value={view}
      exclusive
      onChange={handleChange}
      aria-label={t('viewToggle.ariaLabel')}
      size='small'
    >
      <ToggleButton value='card' aria-label={t('viewToggle.card')}>
        <Tooltip title={t('viewToggle.card')}>
          <SquaresFourIcon fontSize='var(--icon-fontSize-md)' />
        </Tooltip>
      </ToggleButton>
      <ToggleButton value='table' aria-label={t('viewToggle.table')}>
        <Tooltip title={t('viewToggle.table')}>
          <RowsIcon fontSize='var(--icon-fontSize-md)' />
        </Tooltip>
      </ToggleButton>
    </ToggleButtonGroup>
  );
}
