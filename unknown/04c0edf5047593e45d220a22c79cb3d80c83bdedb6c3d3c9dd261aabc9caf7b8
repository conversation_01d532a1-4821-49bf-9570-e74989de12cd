import { redirect } from '@/i18n/navigation';
import type { Metadata } from 'next';
import { getLocale } from 'next-intl/server';
import * as React from 'react';

import { config } from '@/config';

export const metadata = {
  title: `Settings | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const locale = await getLocale();

  // Redirect to the account section by default
  redirect({
    href: '/app/settings/account',
    locale: locale,
  });

  // This will never be reached due to the redirect
  return <></>;
}
