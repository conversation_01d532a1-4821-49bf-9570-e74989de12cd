'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Link } from '@mui/material';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useTranslations } from 'next-intl';
import NextLink from 'next/link';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { useAuth } from '@/contexts/firebase-auth-context';
import { logger } from '@/lib/logger/default-logger';
import { getPathWithReturnTo, getReturnToParam } from '@/lib/return-to-utils';
import { paths } from '@/paths';
import { useSearchParams } from 'next/navigation';

type Values = {
  email: string;
};

const defaultValues = { email: '' } satisfies Values;

export function ResetPasswordForm(): React.JSX.Element {
  const params = useSearchParams();
  const [isPending, setIsPending] = React.useState<boolean>(false);
  const [isSuccess, setIsSuccess] = React.useState<boolean>(false);
  const [errorDismissed, setErrorDismissed] = React.useState<boolean>(false);
  const [formErrorDismissed, setFormErrorDismissed] = React.useState<boolean>(false);
  const { resetPassword, clearError, error: authError } = useAuth();
  const t = useTranslations('auth');

  // Get returnTo parameter for preserving across navigation
  const returnTo = getReturnToParam(params);

  // Create schema with translations
  const schema = React.useMemo(() => {
    return zod.object({
      email: zod
        .string()
        .min(1, { message: t('resetPassword.emailRequired') })
        .email(),
    });
  }, [t]);

  const {
    control,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<Values>({ defaultValues, resolver: zodResolver(schema) });

  const onSubmit = React.useCallback(
    async (values: Values): Promise<void> => {
      setIsPending(true);
      // Reset error dismissed states on new submission
      setErrorDismissed(false);
      setFormErrorDismissed(false);

      try {
        await resetPassword(values.email);
        setIsPending(false);
        setIsSuccess(true);
      } catch (error) {
        // Parse Firebase error message and provide a user-friendly message
        let errorMessage = t('errors.resetFailed');

        // Only in development, log the actual error
        if (process.env.NODE_ENV === 'development') {
          logger.warn('Password reset error:', error);
        }

        // Handle specific error codes
        const firebaseError = error as { code?: string; message: string };
        if (firebaseError.code === 'auth/user-not-found') {
          errorMessage = t('errors.userNotFound');
        } else if (firebaseError.code === 'auth/invalid-email') {
          errorMessage = t('errors.invalidEmail');
        } else if (firebaseError.code === 'auth/too-many-requests') {
          errorMessage = t('errors.tooManyRequests');
        }

        setError('root', { type: 'server', message: errorMessage });
        setIsPending(false);
      }

      // Success message is handled in the try/catch block
    },
    [resetPassword, setError, setErrorDismissed, setFormErrorDismissed, t]
  );

  return (
    <Stack spacing={4}>
      <Stack spacing={1}>
        <Typography variant='h5'>{t('resetPassword.title')}</Typography>
        <Typography color='text.secondary' variant='body2'>
          {t('resetPassword.rememberPassword')}{' '}
          <Link
            component={NextLink}
            href={getPathWithReturnTo(paths.auth.signIn, returnTo)}
            underline='hover'
            variant='subtitle2'
          >
            {t('resetPassword.signInLink')}
          </Link>
        </Typography>
      </Stack>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={2}>
          <Controller
            control={control}
            name='email'
            render={({ field }) => (
              <FormControl error={Boolean(errors.email)}>
                <InputLabel>{t('resetPassword.emailLabel')}</InputLabel>
                <OutlinedInput {...field} label={t('resetPassword.emailLabel')} type='email' />
                {errors.email ? <FormHelperText>{errors.email.message}</FormHelperText> : null}
              </FormControl>
            )}
          />
          {/* Display form validation errors */}
          {errors.root && !formErrorDismissed ? (
            <Alert
              color='error'
              severity='error'
              sx={{ mb: 2 }}
              onClose={() => {
                // We can't directly clear form errors from react-hook-form
                // But we can mark the error as dismissed so it doesn't show
                setFormErrorDismissed(true);
                clearError();
              }}
            >
              {errors.root.message}
            </Alert>
          ) : null}

          {/* Display Firebase authentication errors */}
          {authError && !errors.root && !isSuccess && !errorDismissed ? (
            <Alert
              color='error'
              severity='error'
              sx={{ mb: 2 }}
              onClose={() => {
                clearError();
                setErrorDismissed(true);
              }}
            >
              {authError.includes('auth/user-not-found')
                ? t('errors.userNotFound')
                : authError.includes('auth/invalid-email')
                  ? t('errors.invalidEmail')
                  : authError.includes('auth/')
                    ? t('errors.resetFailed')
                    : t('errors.resetFailed')}
            </Alert>
          ) : null}
          {isSuccess ? (
            <Stack spacing={2}>
              <Alert color='success'>{t('resetPassword.successMessage')}</Alert>
              <Button component={NextLink} href={getPathWithReturnTo(paths.auth.signIn, returnTo)} variant='contained'>
                {t('resetPassword.backToSignIn')}
              </Button>
            </Stack>
          ) : (
            <Button disabled={isPending} type='submit' variant='contained'>
              {t('resetPassword.submitButton')}
            </Button>
          )}
        </Stack>
      </form>
    </Stack>
  );
}
