import Box from '@mui/material/Box';
import * as React from 'react';

import { OnboardingGuard } from '@/components/auth/onboarding-guard';

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps): React.JSX.Element {
  return (
    <OnboardingGuard type='onboardingRequired'>
      <Box
        sx={{
          display: 'flex',
          flex: '1 1 auto',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 'calc(100vh - var(--MainNav-height) - 64px)', // Account for header and padding
        }}
      >
        {children}
      </Box>
    </OnboardingGuard>
  );
}
