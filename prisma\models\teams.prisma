model Team {
    id        String   @id @default(uuid())
    name      String?
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    parentId String?
    parent   Team?   @relation("Parent", fields: [parentId], references: [id])
    children Team[]  @relation("Parent")

    workspaceId String?
    workspace   Workspace? @relation(fields: [workspaceId], references: [id])

    members TeamMembership[]
}

model TeamMembership {
    createdAt DateTime @default(now())

    teamId String
    team   Team   @relation(fields: [teamId], references: [id])

    userId String
    user   User   @relation(fields: [userId], references: [id])

    @@unique([userId, teamId])
}
