import type { <PERSON>ada<PERSON> } from 'next';
import * as React from 'react';

import { IntegrationDetailClient } from '@/components/settings/integrations/integration-detail-client';
import { config } from '@/config';

export const metadata: Metadata = {
  title: `Integration Details | ${config.site.name}`,
};

export default function Page(): React.JSX.Element {
  return <IntegrationDetailClient />;
}
