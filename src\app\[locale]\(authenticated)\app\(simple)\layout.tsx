import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import GlobalStyles from '@mui/material/GlobalStyles';
import Stack from '@mui/material/Stack';
import * as React from 'react';

import { DynamicLogo } from '@/components/core/logo';
import { LogoutButton } from '@/components/layout/logout-button';

interface LayoutProps {
  children: React.ReactNode;
}

/**
 * Simple layout component for pages that need a basic header with logo and logout button
 * Used by onboarding and workspace selection pages
 */
export default function Layout({ children }: LayoutProps): React.JSX.Element {
  return (
    <>
      <GlobalStyles
        styles={{
          body: {
            '--MainNav-height': '56px',
            '--MainNav-zIndex': 1000,
            '--SideNav-width': '280px',
            '--SideNav-zIndex': 1100,
            '--MobileNav-width': '320px',
            '--MobileNav-zIndex': 1100,
          },
        }}
      />
      <Box
        sx={{
          bgcolor: 'var(--mui-palette-background-default)',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          minHeight: '100vh',
        }}
      >
        {/* Simple header with logo and logout */}
        <Box
          component='header'
          sx={{
            borderBottom: '1px solid var(--mui-palette-divider)',
            bgcolor: 'var(--mui-palette-background-paper)',
            position: 'sticky',
            top: 0,
            zIndex: 'var(--MainNav-zIndex)',
          }}
        >
          <Container maxWidth={false}>
            <Stack
              direction='row'
              spacing={2}
              sx={{
                alignItems: 'center',
                justifyContent: 'space-between',
                minHeight: 'var(--MainNav-height)',
                px: 2,
              }}
            >
              <DynamicLogo height={32} width={122} />
              <LogoutButton />
            </Stack>
          </Container>
        </Box>

        {/* Main content */}
        <Box
          component='main'
          sx={{
            display: 'flex',
            flex: '1 1 auto',
            flexDirection: 'column',
          }}
        >
          <Container maxWidth={false} sx={{ py: 4, flex: 1 }}>
            {children}
          </Container>
        </Box>
      </Box>
    </>
  );
}
