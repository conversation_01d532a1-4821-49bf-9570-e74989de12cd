'use client';

import Avatar from '@mui/material/Avatar';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import LinearProgress from '@mui/material/LinearProgress';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { ClockIcon } from '@phosphor-icons/react/dist/ssr/Clock';
import { UserIcon } from '@phosphor-icons/react/dist/ssr/User';
import { WarningIcon } from '@phosphor-icons/react/dist/ssr/Warning';
import type { ApexOptions } from 'apexcharts';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { Chart } from '@/components/core/chart';

export function TeamHealth(): React.JSX.Element {
  const t = useTranslations('insights');
  const theme = useTheme();

  // Sample data for burnout risk
  const burnoutRiskData = [
    { name: 'John Doe', avatar: '/assets/avatar.png', consecutiveHours: 12, offHoursCommits: 8, workloadSpike: 150 },
    { name: 'Jane Smith', avatar: '', consecutiveHours: 6, offHoursCommits: 2, workloadSpike: 110 },
    { name: 'Bob Johnson', avatar: '', consecutiveHours: 10, offHoursCommits: 5, workloadSpike: 130 },
  ];

  // Sample data for workload distribution
  const workloadData = {
    developers: ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Williams', 'Charlie Brown'],
    wip: [5, 2, 4, 3, 1],
    storyPoints: [24, 8, 18, 12, 5],
  };

  // Chart options for workload chart
  const workloadChartOptions: ApexOptions = {
    chart: {
      background: 'transparent',
      toolbar: { show: false },
      zoom: { enabled: false },
    },
    colors: [theme.palette.primary.main, theme.palette.success.main],
    dataLabels: { enabled: false },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 2,
    },
    legend: { show: true, position: 'top' },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: '70%',
        borderRadius: 4,
      },
    },
    stroke: { width: 0 },
    theme: { mode: theme.palette.mode },
    xaxis: {
      axisBorder: { color: theme.palette.divider, show: true },
      axisTicks: { color: theme.palette.divider, show: true },
      categories: workloadData.developers,
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
    yaxis: {
      labels: {
        style: { colors: theme.palette.text.secondary },
      },
    },
  };

  return (
    <Grid container spacing={3}>
      {/* Burnout Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.burnout')} subheader={t('metrics.consecutiveActiveHours')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Stack spacing={2}>
                {burnoutRiskData.map((dev, index) => (
                  <Card key={index} variant='outlined' sx={{ p: 2 }}>
                    <Stack direction='row' spacing={2} alignItems='center'>
                      <Avatar src={dev.avatar}>{!dev.avatar && <UserIcon />}</Avatar>
                      <Stack sx={{ flex: 1 }}>
                        <Stack direction='row' justifyContent='space-between'>
                          <Typography variant='subtitle2'>{dev.name}</Typography>
                          {dev.consecutiveHours > 10 && (
                            <WarningIcon color={theme.palette.error.main} fontSize='var(--icon-fontSize-md)' />
                          )}
                        </Stack>
                        <Stack direction='row' spacing={2}>
                          <Typography variant='caption' color='text.secondary'>
                            <ClockIcon fontSize='var(--icon-fontSize-sm)' /> {dev.consecutiveHours}h consecutive
                          </Typography>
                          <Typography variant='caption' color='text.secondary'>
                            {dev.offHoursCommits} off-hours commits
                          </Typography>
                          <Typography variant='caption' color='text.secondary'>
                            {dev.workloadSpike}% workload spike
                          </Typography>
                        </Stack>
                      </Stack>
                    </Stack>
                  </Card>
                ))}
              </Stack>

              <Typography variant='body2' color='error'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.schedule1on1')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>

      {/* Workload Question */}
      <Grid size={{ md: 6, xs: 12 }}>
        <Card sx={{ height: '100%' }}>
          <CardHeader title={t('questions.workload')} subheader={t('metrics.storyPointDistribution')} />
          <Divider />
          <CardContent>
            <Stack spacing={3}>
              <Chart
                height={250}
                options={workloadChartOptions}
                series={[
                  { name: 'WIP Items', data: workloadData.wip },
                  { name: 'Story Points', data: workloadData.storyPoints },
                ]}
                type='bar'
                width='100%'
              />

              <Stack direction='row' spacing={2} alignItems='center'>
                <Stack sx={{ flex: 1 }}>
                  <Typography variant='body2' color='text.secondary'>
                    Team average: 3 WIP items, 13.4 story points
                  </Typography>
                  <LinearProgress
                    variant='determinate'
                    value={70}
                    color='primary'
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Stack>
              </Stack>

              <Typography variant='body2' color='primary'>
                <ArrowRightIcon fontSize='var(--icon-fontSize-sm)' />
                {t('actions.rebalanceBacklog')}
              </Typography>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
}
