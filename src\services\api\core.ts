import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

import { logger } from '@/lib/logger/default-logger';

// TODO: USE https://github.com/OpenAPITools/openapi-generator TO GENERATE CLIENT LIBRARIES
// TODO: THEN USE THIS CORE/SERVER TO ALLOW US TO INJECT DIFFERENT TOKEN PROVIDERS INTO GENERATED CLIENT LIBRARIES

/**
 * Token provider interface
 * This allows us to inject different token strategies for client and server
 */
export interface TokenProvider {
  /**
   * Get the authentication token
   * @returns A promise that resolves to the token or undefined
   */
  getToken(): Promise<string | undefined>;
}

/**
 * Workspace provider interface
 * This allows us to inject workspace ID into API requests
 */
export interface WorkspaceProvider {
  /**
   * Get the current workspace ID
   * @returns A promise that resolves to the workspace ID or undefined
   */
  getWorkspaceId(): Promise<string | undefined>;
}

/**
 * Core API service that can be used in both client and server environments
 */
export class CoreApiService {
  private api: AxiosInstance;
  private tokenProvider: TokenProvider;
  private workspaceProvider?: WorkspaceProvider;

  /**
   * Create a new CoreApiService
   * @param tokenProvider The token provider to use for authentication
   * @param workspaceProvider The workspace provider to use for workspace context (optional)
   * @param baseURL The base URL for API requests
   */
  constructor(tokenProvider: TokenProvider, workspaceProvider?: WorkspaceProvider, baseURL?: string) {
    this.tokenProvider = tokenProvider;
    this.workspaceProvider = workspaceProvider;

    // Use the provided base URL or the environment variable
    const apiBaseURL = baseURL || process.env.NEXT_PUBLIC_API_URL;

    this.api = axios.create({
      baseURL: apiBaseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Set up request interceptor for authentication
    this.setupRequestInterceptor();

    // Set up response interceptor for error handling
    this.setupResponseInterceptor();
  }

  /**
   * Set up the request interceptor to add authentication token and workspace ID
   */
  private setupRequestInterceptor(): void {
    this.api.interceptors.request.use(
      async (config) => {
        try {
          // Add authentication token
          const token = await this.tokenProvider.getToken();
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }

          // Add workspace ID if available
          if (this.workspaceProvider) {
            const workspaceId = await this.workspaceProvider.getWorkspaceId();
            if (workspaceId) {
              config.headers['X-Workspace-ID'] = workspaceId;
            }
          }
        } catch (error) {
          console.error('Error setting up request headers:', error);
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }

  /**
   * Set up the response interceptor for error handling
   */
  private setupResponseInterceptor(): void {
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        // Handle specific error cases
        if (error.response) {
          // Server responded with a status code outside of 2xx
          if (error.response.status === 401) {
            // Unauthorized - token expired or invalid
            logger.warn('Authentication error:', error.response.data);
          } else if (error.response.status === 403) {
            // Forbidden - insufficient permissions
            logger.warn('Permission denied:', error.response.data);
          } else if (error.response.status === 404) {
            // Not found
            logger.warn('Resource not found:', error.response.data);
          } else {
            // Other error
            logger.warn('API error:', error.response.data);
          }
        } else if (error.request) {
          // Request was made but no response received
          logger.warn('No response received:', error.request);
        } else {
          // Error in setting up the request
          logger.warn('Request error:', error.message);
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Generic request method
   * @param config The Axios request configuration
   * @returns A promise that resolves to the response data
   */
  async request<T>(config: AxiosRequestConfig): Promise<T> {
    const response = await this.api.request<T>(config);
    return response.data;
  }

  /**
   * GET request
   * @param url The URL to request
   * @param params The query parameters
   * @returns A promise that resolves to the response data
   */
  async get<T, P = Record<string, unknown>>(url: string, params?: P): Promise<T> {
    const response = await this.api.get<T>(url, { params });
    return response.data;
  }

  /**
   * POST request
   * @param url The URL to request
   * @param data The request body
   * @returns A promise that resolves to the response data
   */
  async post<T, D = Record<string, unknown>>(url: string, data?: D): Promise<T> {
    const response = await this.api.post<T>(url, data);
    return response.data;
  }

  /**
   * PUT request
   * @param url The URL to request
   * @param data The request body
   * @returns A promise that resolves to the response data
   */
  async put<T, D = Record<string, unknown>>(url: string, data?: D): Promise<T> {
    const response = await this.api.put<T>(url, data);
    return response.data;
  }

  /**
   * PATCH request
   * @param url The URL to request
   * @param data The request body
   * @returns A promise that resolves to the response data
   */
  async patch<T, D = Record<string, unknown>>(url: string, data?: D): Promise<T> {
    const response = await this.api.patch<T>(url, data);
    return response.data;
  }

  /**
   * DELETE request
   * @param url The URL to request
   * @returns A promise that resolves to the response data
   */
  async delete<T>(url: string): Promise<T> {
    const response = await this.api.delete<T>(url);
    return response.data;
  }
}
