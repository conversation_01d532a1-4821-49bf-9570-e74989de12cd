'use client';

import { Link, useRouter } from '@/i18n/navigation';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { ChartLineIcon } from '@phosphor-icons/react/dist/ssr/ChartLine';
import { GitBranchIcon } from '@phosphor-icons/react/dist/ssr/GitBranch';
import { RocketIcon } from '@phosphor-icons/react/dist/ssr/Rocket';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { DynamicLogo } from '@/components/core/logo';
import { useAuth } from '@/contexts/firebase-auth-context';
import { paths } from '@/paths';

export function LandingPage(): React.JSX.Element {
  const theme = useTheme();
  const router = useRouter();
  const { user, loading } = useAuth();
  const t = useTranslations('landing');

  const handleGetStarted = React.useCallback(() => {
    if (user) {
      router.push(paths.root);
    } else {
      router.push(paths.auth.signIn);
    }
  }, [user, router]);
  const features = [
    {
      icon: <ChartLineIcon fontSize='var(--icon-fontSize-xl)' />,
      title: t('features.delivery.title'),
      description: t('features.delivery.description'),
    },
    {
      icon: <GitBranchIcon fontSize='var(--icon-fontSize-xl)' />,
      title: t('features.git.title'),
      description: t('features.git.description'),
    },
    {
      icon: <RocketIcon fontSize='var(--icon-fontSize-xl)' />,
      title: t('features.team.title'),
      description: t('features.team.description'),
    },
  ];

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <DynamicLogo height={60} width={60} />
      </Box>
    );
  }

  return (
    <Box
      sx={() => ({
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        background: 'linear-gradient(135deg, rgba(63, 81, 181, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)',
      })}
    >
      {/* Header */}
      <Box
        component='header'
        sx={{
          py: 2,
          px: 3,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: '1px solid',
          borderColor: 'divider',
          backdropFilter: 'blur(10px)',
          bgcolor: 'background.paper',
          opacity: 0.9,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <DynamicLogo height={40} width={40} />
          <Typography
            variant='h6'
            sx={{
              fontWeight: 600,
              background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            BMS Pulse
          </Typography>
        </Box>
        <Stack direction='row' spacing={1}>
          {user ? (
            <Button
              variant='contained'
              onClick={handleGetStarted}
              endIcon={<ArrowRightIcon fontSize='var(--icon-fontSize-md)' />}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 600,
              }}
            >
              {t('hero.enterApp')}
            </Button>
          ) : (
            <>
              <Button
                component={Link}
                href={paths.auth.signIn}
                variant='text'
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 500,
                }}
              >
                {t('hero.signIn')}
              </Button>
              <Button
                component={Link}
                href={paths.auth.signUp}
                variant='contained'
                sx={{
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 600,
                }}
              >
                {t('hero.getStartedFree')}
              </Button>
            </>
          )}
        </Stack>
      </Box>

      {/* Main Content */}
      <Container maxWidth='lg' sx={{ flex: 1, py: { xs: 6, md: 10 } }}>
        {' '}
        <Grid container spacing={6} alignItems='center'>
          {/* Hero Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Stack spacing={4}>
              <Box>
                <Typography
                  variant='h1'
                  sx={{
                    fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                    fontWeight: 700,
                    lineHeight: 1.1,
                    mb: 2,
                    background: 'linear-gradient(135deg, #333333, #666666)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                  }}
                >
                  {t('hero.title').split(' ').slice(0, 2).join(' ')}
                  <br />
                  {t('hero.title').split(' ').slice(2, 3).join(' ')}
                  <br />
                  <Box
                    component='span'
                    sx={{
                      background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}
                  >
                    {t('hero.title').split(' ').slice(3).join(' ')}
                  </Box>
                </Typography>
                <Typography
                  variant='h5'
                  color='text.secondary'
                  sx={{
                    fontWeight: 400,
                    lineHeight: 1.5,
                    maxWidth: '500px',
                  }}
                >
                  {t('hero.subtitle')}
                </Typography>
              </Box>

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <Button
                  size='large'
                  variant='contained'
                  onClick={handleGetStarted}
                  endIcon={<ArrowRightIcon fontSize='var(--icon-fontSize-md)' />}
                  sx={{
                    py: 1.5,
                    px: 4,
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: '1.1rem',
                    background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    '&:hover': {
                      background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
                    },
                  }}
                >
                  {user ? t('hero.enterApp') : t('hero.getStartedFree')}
                </Button>
                {!user && (
                  <Button
                    component={Link}
                    href={paths.auth.signIn}
                    size='large'
                    variant='outlined'
                    sx={{
                      py: 1.5,
                      px: 4,
                      borderRadius: 3,
                      textTransform: 'none',
                      fontWeight: 600,
                      fontSize: '1.1rem',
                      borderColor: 'primary.main',
                      color: 'primary.main',
                      '&:hover': {
                        borderColor: 'primary.dark',
                        bgcolor: 'primary.50',
                      },
                    }}
                  >
                    {t('hero.signIn')}
                  </Button>
                )}
              </Stack>
            </Stack>
          </Grid>{' '}
          {/* Visual Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Box
              sx={{
                position: 'relative',
                height: { xs: 300, md: 400 },
                borderRadius: 4,
                background: 'linear-gradient(135deg, rgba(63, 81, 181, 0.1), rgba(156, 39, 176, 0.1))',
                border: '1px solid',
                borderColor: 'primary.200',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background:
                    'radial-gradient(circle at 30% 40%, rgba(63, 81, 181, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(156, 39, 176, 0.2) 0%, transparent 50%)',
                },
              }}
            >
              <DynamicLogo height={120} width={120} />
            </Box>
          </Grid>
        </Grid>
        {/* Features Section */}
        <Box sx={{ mt: { xs: 8, md: 12 } }}>
          <Typography
            variant='h3'
            align='center'
            sx={{
              fontWeight: 700,
              mb: 2,
              fontSize: { xs: '1.8rem', md: '2.5rem' },
            }}
          >
            {t('features.title')}
          </Typography>
          <Typography
            variant='h6'
            align='center'
            color='text.secondary'
            sx={{
              fontWeight: 400,
              mb: 6,
              maxWidth: '600px',
              mx: 'auto',
            }}
          >
            {t('features.subtitle')}
          </Typography>

          <Grid container spacing={4}>
            {' '}
            {features.map((feature, index) => (
              <Grid size={{ xs: 12, md: 4 }} key={index}>
                <Box
                  sx={{
                    p: 4,
                    height: '100%',
                    borderRadius: 3,
                    bgcolor: 'background.paper',
                    border: '1px solid',
                    borderColor: 'divider',
                    backdropFilter: 'blur(10px)',
                    opacity: 0.9,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 3,
                      borderColor: 'primary.300',
                    },
                  }}
                >
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: 2,
                      background: 'linear-gradient(135deg, rgba(63, 81, 181, 0.1), rgba(156, 39, 176, 0.1))',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 3,
                      color: 'primary.main',
                    }}
                  >
                    {feature.icon}
                  </Box>
                  <Typography variant='h6' sx={{ fontWeight: 600, mb: 2 }}>
                    {feature.title}
                  </Typography>
                  <Typography color='text.secondary' sx={{ lineHeight: 1.6 }}>
                    {feature.description}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Container>

      {/* Footer */}
      <Box
        component='footer'
        sx={{
          py: 4,
          px: 3,
          mt: 'auto',
          borderTop: '1px solid',
          borderColor: 'divider',
          bgcolor: 'background.paper',
          backdropFilter: 'blur(10px)',
          opacity: 0.9,
        }}
      >
        <Container maxWidth='lg'>
          <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent='space-between' alignItems='center' spacing={2}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <DynamicLogo height={24} width={24} />{' '}
              <Typography variant='body2' color='text.secondary'>
                {t('footer.copyright')}
              </Typography>
            </Box>
            <Typography variant='body2' color='text.secondary'>
              {t('footer.tagline')}
            </Typography>
          </Stack>
        </Container>
      </Box>
    </Box>
  );
}
