import type { Icon } from '@phosphor-icons/react/dist/lib/types';
import { BugIcon } from '@phosphor-icons/react/dist/ssr/Bug';
import { ChartLineIcon } from '@phosphor-icons/react/dist/ssr/ChartLine';
import { ChartPieIcon } from '@phosphor-icons/react/dist/ssr/ChartPie';
import { CurrencyDollarIcon } from '@phosphor-icons/react/dist/ssr/CurrencyDollar';
import { FlowArrowIcon } from '@phosphor-icons/react/dist/ssr/FlowArrow';
import { GearSixIcon } from '@phosphor-icons/react/dist/ssr/GearSix';
import { GitBranchIcon } from '@phosphor-icons/react/dist/ssr/GitBranch';
import { GitCommitIcon } from '@phosphor-icons/react/dist/ssr/GitCommit';
import { GitPullRequestIcon } from '@phosphor-icons/react/dist/ssr/GitPullRequest';
import { HeartIcon } from '@phosphor-icons/react/dist/ssr/Heart';
import { HourglassIcon } from '@phosphor-icons/react/dist/ssr/Hourglass';
import { HouseIcon } from '@phosphor-icons/react/dist/ssr/House';
import { LightbulbIcon } from '@phosphor-icons/react/dist/ssr/Lightbulb';
import { PlugsConnectedIcon } from '@phosphor-icons/react/dist/ssr/PlugsConnected';
import { TrophyIcon } from '@phosphor-icons/react/dist/ssr/Trophy';
import { UserIcon } from '@phosphor-icons/react/dist/ssr/User';
import { UsersIcon } from '@phosphor-icons/react/dist/ssr/Users';
import { XSquare } from '@phosphor-icons/react/dist/ssr/XSquare';

export const navIcons = {
  home: HouseIcon,
  'chart-pie': ChartPieIcon,
  'gear-six': GearSixIcon,
  lightbulb: LightbulbIcon,
  'plugs-connected': PlugsConnectedIcon,
  'x-square': XSquare,
  user: UserIcon,
  users: UsersIcon,
  // Engineering Insights Icons
  bug: BugIcon,
  'chart-line': ChartLineIcon,
  'currency-dollar': CurrencyDollarIcon,
  'flow-arrow': FlowArrowIcon,
  heart: HeartIcon,
  hourglass: HourglassIcon,
  trophy: TrophyIcon,
  // Git Icons
  'git-branch': GitBranchIcon,
  'git-commit': GitCommitIcon,
  'git-pull-request': GitPullRequestIcon,
} as Record<string, Icon>;
