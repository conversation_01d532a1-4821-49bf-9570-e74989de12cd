import { defineRouting } from 'next-intl/routing';

import { Language } from '@/lib/models/language';
import { paths } from '@/paths';

export const routing = defineRouting({
  locales: Object.values(Language),
  defaultLocale: Language.PT_BR,
  localePrefix: 'never',
  localeDetection: true,
  pathnames: {
    [paths.landing]: paths.landing,
    [paths.root]: paths.root,
    [paths.onboarding]: {
      ['pt-BR']: '/app/onboarding',
      es: '/app/onboarding',
    },
    [paths.workspaceSelection]: {
      ['pt-BR']: '/app/selecao-workspace',
      es: '/app/seleccion-workspace',
    },
    [paths.insights.delivery]: {
      ['pt-BR']: '/app/insights/entrega',
      es: '/app/insights/entrega',
    },
    [paths.insights.quality]: {
      ['pt-BR']: '/app/insights/qualidade',
      es: '/app/insights/calidad',
    },
    [paths.insights.teamHealth]: {
      ['pt-BR']: '/app/insights/saude-equipe',
      es: '/app/insights/salud-equipo',
    },
    [paths.insights.process]: {
      ['pt-BR']: '/app/insights/processo',
      es: '/app/insights/proceso',
    },
    [paths.insights.business]: {
      ['pt-BR']: '/app/insights/negocio',
      es: '/app/insights/negocio',
    },
    [paths.insights.benchmarks]: {
      ['pt-BR']: '/app/insights/benchmarks',
      es: '/app/insights/benchmarks',
    },
    [paths.insights.cost]: {
      ['pt-BR']: '/app/insights/custo',
      es: '/app/insights/costo',
    },
    [paths.customers]: {
      ['pt-BR']: '/app/clientes',
      es: '/app/clientes',
    },
    // Git section
    [paths.git.overview]: {
      ['pt-BR']: '/app/git/visao-geral',
      es: '/app/git/resumen',
    },
    [paths.git.repositories]: {
      ['pt-BR']: '/app/git/repositorios',
      es: '/app/git/repositorios',
    },
    [paths.git.pullRequests]: {
      ['pt-BR']: '/app/git/pull-requests',
      es: '/app/git/pull-requests',
    },
    [paths.git.issues]: {
      ['pt-BR']: '/app/git/issues',
      es: '/app/git/issues',
    },
    [paths.settings.integrations.index]: {
      ['pt-BR']: '/app/configuracoes/integracoes',
      es: '/app/configuracion/integraciones',
    },
    [paths.settings.integrations.details]: {
      ['pt-BR']: '/app/configuracoes/integracoes/detalhes',
      es: '/app/configuracion/integraciones/detalles',
    },
    [paths.settings.index]: {
      ['pt-BR']: '/app/configuracoes',
      es: '/app/configuracion',
    },
    [paths.settings.account]: {
      ['pt-BR']: '/app/configuracoes/conta',
      es: '/app/configuracion/cuenta',
    },
    [paths.settings.security]: {
      ['pt-BR']: '/app/configuracoes/seguranca',
      es: '/app/configuracion/seguridad',
    },
    [paths.settings.notifications]: {
      ['pt-BR']: '/app/configuracoes/notificacoes',
      es: '/app/configuracion/notificaciones',
    },
    [paths.settings.billing]: {
      ['pt-BR']: '/app/configuracoes/faturamento',
      es: '/app/configuracion/facturacion',
    },
    [paths.settings.team]: {
      ['pt-BR']: '/app/configuracoes/equipe',
      es: '/app/configuracion/equipo',
    },
    [paths.settings.preferences]: {
      ['pt-BR']: '/app/configuracoes/preferencias',
      es: '/app/configuracion/preferencias',
    },
    // Auth routes
    [paths.auth.signIn]: {
      ['pt-BR']: '/entrar',
      es: '/iniciar-sesion',
    },
    [paths.auth.signUp]: {
      ['pt-BR']: '/cadastrar',
      es: '/registrarse',
    },
    [paths.auth.resetPassword]: {
      ['pt-BR']: '/redefinir-senha',
      es: '/restablecer-contrasena',
    },
    // Error routes
    [paths.errors.notFound]: {
      ['pt-BR']: '/erros/nao-encontrado',
      es: '/errores/no-encontrado',
    },
  },
});
