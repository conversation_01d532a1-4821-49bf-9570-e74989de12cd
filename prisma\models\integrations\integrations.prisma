enum IntegrationStatus {
    Active
    Inactive
    Suspended
    Uninstalled
    Synchronizing
    PermissionsOutdated
}

enum IntegrationChannel {
    GitHub
    GitLab
    Bitbucket
    AzureDevOps
    Slack
    Jira
    Email
}

model WorkspaceIntegration {
    id String @id @default(uuid())

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
    workspaceId String

    channel IntegrationChannel
    status  IntegrationStatus

    // The id of the integration in the channel.
    // For example on GitHub, this is the installation id.
    integrationIdOnChannel String

    /// GitHub integration data.
    /// Only available if channel is GitHub.
    github GitHubIntegration?

    // Other Relations
    gitRepositories GitRepository[] @ignore

    @@index([workspaceId, channel])
    @@index([integrationIdOnChannel, channel])
}

model IntegrationState {
    id String @id @default(uuid())

    createdAt DateTime @default(now())

    workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
    workspaceId String

    channel IntegrationChannel

    @@unique(name: "unique", [workspaceId, channel])
    @@index([createdAt])
}

/// Profiles are how we link integrations activities to a user on BMS Pulse.
model IntegrationProfile {
    id String @id @default(uuid())

    workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
    workspaceId String

    channel IntegrationChannel

    /// The id of this profile in the channel.
    /// May be null if there is not enough information to determine the profile.
    idOnChannel String?

    username String?
    name     String?
    emails   String[]

    links UserProfileLink[]

    authoredCommits GitCommit[] @relation("authoredCommits") @ignore
    commitedCommits GitCommit[] @relation("commitedCommits") @ignore
    pushedCommits   GitCommit[] @relation("pushedCommits") @ignore

    tasks Task[] @ignore

    closedPullRequests  GitPullRequest[] @relation("closedPullRequests") @ignore
    createdPullRequests GitPullRequest[] @relation("createdPullRequests") @ignore

    // Git repository relations
    createdRepositories  GitRepository[] @relation("createdRepository") @ignore
    deletedRepositories  GitRepository[] @relation("deletedRepository") @ignore
    archivedRepositories GitRepository[] @relation("archivedRepository") @ignore

    @@unique(name: "unique", [channel, idOnChannel, username, workspaceId])
    @@index([emails, workspaceId])
    @@index([idOnChannel, channel, workspaceId])
    @@index([username, channel, workspaceId])
}
