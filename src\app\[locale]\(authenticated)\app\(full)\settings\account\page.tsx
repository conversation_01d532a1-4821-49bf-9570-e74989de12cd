import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { AccountDetailsForm } from '@/components/settings/account/account-details-form';
import { config } from '@/config';

export const metadata = {
  title: `Account | Settings | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('settings.account');

  return (
    <Stack spacing={3}>
      <Typography variant='h4'>{t('title')}</Typography>
      <AccountDetailsForm />
    </Stack>
  );
}
