import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { ArrowLeftIcon } from '@phosphor-icons/react/dist/ssr/ArrowLeft';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import NextLink from 'next/link';
import * as React from 'react';

import { config } from '@/config';
import { paths } from '@/paths';

export const metadata = {
  title: `Not found | Errors | ${config.site.name}`,
} satisfies Metadata;

export default async function NotFound(): Promise<React.JSX.Element> {
  const t = await getTranslations('errors.notFound');

  return (
    <Box
      component='main'
      sx={{
        alignItems: 'center',
        display: 'flex',
        justifyContent: 'center',
        minHeight: '100%',
      }}
    >
      <Stack spacing={3} sx={{ alignItems: 'center', maxWidth: 'md' }}>
        <Box>
          <Box
            component='img'
            alt={t('altText')}
            src='/assets/error-404.png'
            sx={{
              display: 'inline-block',
              height: 'auto',
              maxWidth: '100%',
              width: '400px',
            }}
          />
        </Box>
        <Typography variant='h3' sx={{ textAlign: 'center' }}>
          {t('title')}
        </Typography>
        <Typography color='text.secondary' variant='body1' sx={{ textAlign: 'center' }}>
          {t('description')}
        </Typography>
        <Button
          component={NextLink}
          href={paths.root}
          startIcon={<ArrowLeftIcon fontSize='var(--icon-fontSize-md)' />}
          variant='contained'
        >
          {t('action')}
        </Button>
      </Stack>
    </Box>
  );
}
