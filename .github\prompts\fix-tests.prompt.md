---
mode: 'agent'
tools: ['codebase', 'get-library-docs', 'resolve-library-id', 'changes', 'terminalLastCommand', 'terminalSelection', 'findTestFiles', 'searchResults', 'problems', 'usages', 'testFailure' ]
description: 'Fix failing tests in codebase'
---

# Test Fixing Prompt

I need you to help me fix failing tests in my codebase. Please follow these instructions carefully:

## Steps to Fix Tests

1. First, run the tests using:

   ```
   npm run test:coverage
   ```

   Coverage files for each file are saved in the coverage folder #file:../../TestResults/coverage/clover.xml

2. Analyze the test output and identify all failing tests.

3. Before fixing the tests, understand recent changes that might have affected tests.
   First identity which file is being tested (the file without the `.test` in its name).
   Then, use #tool:changes to see the latest changes of both the test files and the file being tested.

   Check how we setup jest on #file:../../jest.setup.js and do not mock unecessary things that are already configured in there.

4. Based on the failing tests and the code changes shown in the diff:

   - Identify why each test is failing
   - Determine what modifications are needed to make the tests pass
   - If you need updated documentation about any library used, use #tool:resolve-library-id and #tool:get-library-docs to find the library documentation
   - Fix test files to align with the current implementation. IF YOU HAVE CONFIDENCE THERE IS AN ERROR ON THE IMPLEMENTATION ITSELF, YOU CAN FIX IT

5. For each test file that needs changes:

   - Explain the issue with the current test
   - Show the modified test code
   - Explain your changes
  
6. After making changes to fix a test file, run:

   ```
   npm run test:coverage <testFilePath>
   ```

   TO CHECK IF CHANGES ARE CORRECT, ALWAYS PREFER RUNNING TESTS TO THE SINGLE TEST
   DO NOT USE INTERNAL TOOL TO RUN TESTS, ALWAYS RUN TESTS WITH TERMINAL `npm` COMMAND

## Reminders

- Preserve the original test intent where possible
- If tests need significant changes, explain why in detail
- If you're uncertain about the expected behavior, explain your assumptions