# GitHub App Setup Guide

This guide will walk you through the process of creating and configuring a GitHub App for BMS Tech Pulse.

## Prerequisites

- GitHub account
- BMS Tech Pulse application running and accessible via a public URL or ngrok for webhook delivery

## Steps to Create a GitHub App

### 1. Create a GitHub App

1. Navigate to [GitHub Developer Settings](https://github.com/settings/apps) and click on "New GitHub App".

2. Configure the basic information:
   - **GitHub App Name**: App Name
   - **Description**: Development analytics platform for GitHub repositories
   - **Homepage URL**: Your application's homepage URL (e.g., `https://your-domain.com`)
   - **Callback URL**: `https://<your-domain.com>/api/integration/callback/github`
   - **Setup URL**: Leave blank
   - **Webhook URL**: `https://<your-domain.com>.hosted.app/api/integration/webhook/github`
   - **Webhook Secret**: Generate a secure random string (e.g., using `openssl rand -hex 20`)

3. Set required permissions:

   **Repository Permissions:**
   - **Administration**: Read-only
   - **Contents**: Read-only
   - **Metadata**: Read-only
   - **Pull requests**: Read-only
   - **Commit statuses**: Read-only
   - **Issues**: Read-only
   - **Checks**: Read-only

   **Organization Permissions:**
   - **API Insights**: Read-only
   - **Members**: Read-only
   - **Administration**: Read-only
   - **Projects**: Read-only

   **Account Permissions:**
   - **Email addresses**: Read-only
   - **Copilot Chat**: Read-only
   - **Copilot Editor Context**: Read-only

4. Subscribe to events:
   - Installation target
   - Member
   - Membership
   - Organization
   - Pull request
   - Pull request review
   - Pull request comment
   - Pull request review thread
   - Repository
   - Team
   - Team add
   - Push
   - Commit comment
   - Issues
   - Issue comment
   - Create
   - Delete
   - Workflow run
   - Check run
   - Check suite

5. For "Where can this GitHub App be installed?", choose "Any account" to allow installation on any GitHub account or organization.

6. Click "Create GitHub App" to finalize the creation process.

### 2. Generate a Private Key

After creating your GitHub App:

1. On your GitHub App's page, scroll down to the "Private keys" section
2. Click "Generate a private key"
3. Save the downloaded .pem file securely

### 3. Configure Environment Variables

Update your application `.env` file with the following GitHub App information:

```
# GitHub App Configuration
GITHUB_APP_ID=your-app-id
GITHUB_APP_NAME=bms-tech-pulse
GITHUB_APP_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----\nYour private key content with \n for line breaks\n-----END RSA PRIVATE KEY-----"
GITHUB_WEBHOOK_SECRET=your-webhook-secret
GITHUB_CLIENT_ID=your-client-id
GITHUB_CLIENT_SECRET=your-client-secret
```

> GITHUB_WEBHOOK_SECRET accepts multiple values separated by a comma.
>
> This is useful to rotate the webhook secret without breaking the app.

**Important Notes for Private Key:**
- When adding the private key to your environment variables, replace actual newlines with `\n`
- Make sure to include the `-----BEGIN RSA PRIVATE KEY-----` and `-----END RSA PRIVATE KEY-----` lines
- Enclose the entire key in quotes

### 4. Install the GitHub App

1. Go to your GitHub App's public page: `https://github.com/apps/your-app-name`
2. Click "Install" or "Configure"
3. Select the organization or account where you want to install the app
4. Choose which repositories to give access to (all or select specific ones)
5. Click "Install"

### 5. Testing the GitHub App

After installation, you should:

1. Verify webhook delivery:
   - Make a small change to one of the repositories where the app is installed
   - Check the "Recent Deliveries" section in your GitHub App settings
   - Verify that the webhook was delivered successfully

2. Check application logs:
   ```bash
   docker-compose logs application
   ```
   Look for messages indicating that the webhook was received and processed.

3. Test API access:
   - Use the GitHub App installation to make an authenticated API request
   - Verify that the request succeeds and returns the expected data

## Webhook Testing with ngrok

For local development, you can use ngrok to expose your local webhook endpoint to the internet:

1. Install ngrok: https://ngrok.com/download
2. Start your application server locally
3. Run ngrok to create a tunnel:
   ```bash
   ngrok http 3000
   ```
4. Update your GitHub App's webhook URL to the ngrok URL (e.g., `https://abc123.ngrok.io/api/webhooks/github`)
5. Test webhook delivery by triggering events in your GitHub repository

## Troubleshooting

### Common Issues

1. **Webhook Delivery Failures**:
   - Check that your webhook URL is accessible from the internet
   - Verify that the webhook secret matches between GitHub and your application
   - Look for errors in the "Recent Deliveries" section of your GitHub App settings

2. **Authentication Issues**:
   - Ensure the private key is correctly formatted in your environment variables
   - Check that the App ID is correct
   - Verify that the installation ID is being correctly stored and used

3. **Permission Issues**:
   - If you're not receiving certain data, check that you've enabled the correct permissions
   - Some permissions may require re-installation of the app after changes

4. **Rate Limiting**:
   - GitHub API has rate limits that apply to GitHub Apps
   - Implement proper caching and rate limit handling in your application

## GitHub App API Usage

The BMS Tech Pulse application uses the GitHub App to authenticate API requests using the following flow:
TODO

## Security Considerations

- Keep your private key secure and never commit it to version control
- Rotate your private key periodically for enhanced security
- Use the webhook secret to verify that webhook payloads are coming from GitHub
- Implement proper error handling for GitHub API requests
