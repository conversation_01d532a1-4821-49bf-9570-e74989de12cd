import { Permission, Prisma } from '@prisma/client';

import { db } from '@/services/db';

const defaultRoles: Prisma.RoleCreateInput[] = [
  {
    id: 'owner',
    permissions: {
      connectOrCreate: [
        { where: { id: Permission.DELETE_WORKSPACE }, create: { id: Permission.DELETE_WORKSPACE } },
        { where: { id: Permission.UPDATE_WORKSPACE }, create: { id: Permission.UPDATE_WORKSPACE } },
        { where: { id: Permission.INVITE_MEMBER }, create: { id: Permission.INVITE_MEMBER } },
        { where: { id: Permission.MANAGE_INTEGRATIONS }, create: { id: Permission.MANAGE_INTEGRATIONS } },
      ],
    },
  },
  {
    id: 'admin',
    permissions: {
      connectOrCreate: [
        { where: { id: Permission.UPDATE_WORKSPACE }, create: { id: Permission.UPDATE_WORKSPACE } },
        { where: { id: Permission.INVITE_MEMBER }, create: { id: Permission.INVITE_MEMBER } },
        { where: { id: Permission.MANAGE_INTEGRATIONS }, create: { id: Permission.MANAGE_INTEGRATIONS } },
      ],
    },
  },
  {
    id: 'member',
    permissions: {
      connectOrCreate: [{ where: { id: 'member' }, create: { id: 'member' } }],
    },
  },
];

export async function main() {
  for (const r of defaultRoles) {
    await db.role.upsert({ where: { id: r.id }, update: r, create: r });
  }
}

main();
