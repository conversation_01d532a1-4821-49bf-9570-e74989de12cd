import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { CostResources } from '@/components/insights/cost-resources';
import { config } from '@/config';

export const metadata = {
  title: `Cost & Resources | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('insights');

  return (
    <Stack spacing={3}>
      <Typography variant='h4'>{t('categories.costResources')}</Typography>
      <Typography color='text.secondary' variant='body1'>
        Tracking resource utilization and optimizing cost efficiency in development projects.
      </Typography>

      <CostResources />
    </Stack>
  );
}
