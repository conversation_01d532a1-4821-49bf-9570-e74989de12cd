import { paths } from '@/paths';
import type { NavItemConfig } from '@/types/nav';

export const navItems = [
  {
    key: 'home',
    title: 'home',
    icon: 'home',
    href: paths.root,
  },
  {
    key: 'insights',
    title: 'insights',
    icon: 'lightbulb',
    items: [
      // Engineering Insights Categories
      {
        key: 'delivery',
        title: 'delivery',
        href: paths.insights.delivery,
        icon: 'hourglass',
      },
      {
        key: 'quality',
        title: 'quality',
        href: paths.insights.quality,
        icon: 'bug',
      },
      {
        key: 'teamHealth',
        title: 'teamHealth',
        href: paths.insights.teamHealth,
        icon: 'heart',
      },
      {
        key: 'process',
        title: 'process',
        href: paths.insights.process,
        icon: 'flow-arrow',
      },
      {
        key: 'business',
        title: 'business',
        href: paths.insights.business,
        icon: 'chart-line',
      },
      {
        key: 'benchmarks',
        title: 'benchmarks',
        href: paths.insights.benchmarks,
        icon: 'trophy',
      },
      {
        key: 'cost',
        title: 'cost',
        href: paths.insights.cost,
        icon: 'currency-dollar',
      },
    ],
  },
  // Git Parent Menu
  {
    key: 'git',
    title: 'git',
    icon: 'git-branch',
    items: [
      {
        key: 'gitOverview',
        title: 'gitOverview',
        href: paths.git.overview,
        icon: 'chart-pie',
      },
      {
        key: 'repositories',
        title: 'repositories',
        href: paths.git.repositories,
        icon: 'git-branch',
      },
      {
        key: 'pullRequests',
        title: 'pullRequests',
        href: paths.git.pullRequests,
        icon: 'git-pull-request',
      },
      {
        key: 'commits',
        title: 'commits',
        href: paths.git.commits,
        icon: 'git-commit',
      },
    ],
  },
  // Other Analytics Items
  {
    key: 'customers',
    title: 'customers',
    href: paths.customers,
    icon: 'users',
  },
  // Settings (outside of any parent menu)
  {
    key: 'settings',
    title: 'settings',
    href: paths.settings.index,
    icon: 'gear-six',
    matcher: {
      type: 'startsWith',
      href: paths.settings.index,
    },
  },
] satisfies NavItemConfig[];
