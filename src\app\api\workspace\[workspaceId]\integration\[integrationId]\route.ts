import { logger } from '@/lib/logger/default-logger';
import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import { getInstance } from '@/services/github/app';
import { IntegrationStatus, Permission } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * /api/workspace/{workspaceId}/integration/{integrationId}:
 *   delete:
 *     summary: Uninstall a specific installation from a workspace integration
 *     description: Removes a specific installation from a workspace integration and uninstalls it from the channel if no other workspace is using it
 *     tags:
 *       - Integration
 *       - Workspace
 *     parameters:
 *       - name: workspaceId
 *         in: path
 *         required: true
 *         description: The workspace ID
 *         schema:
 *           type: string
 *       - name: integrationId
 *         in: path
 *         required: true
 *         description: The integration ID to uninstall
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Installation successfully uninstalled
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 executedOnChannel:
 *                   type: boolean
 *                   description: Whether the uninstall was executed on the integration channel (e.g., GitHub)
 *                 otherWorkspaces:
 *                   type: array
 *                   description: List of other workspaces using this installation
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         $ref: '#/components/schemas/Workspace/properties/id'
 *                       name:
 *                         $ref: '#/components/schemas/Workspace/properties/name'
 *       401:
 *         description: Unauthorized - user not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       403:
 *         description: Forbidden - user lacks MANAGE_INTEGRATIONS permission in the workspace
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Forbidden"
 *       404:
 *         description: Integration not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "The GitHub integration for this workspace does not exist"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *     security:
 *       - BearerAuth: []
 */
export async function DELETE(
  _request: NextRequest,
  props: { params: Promise<{ workspaceId: string; integrationId: string }> }
) {
  try {
    const params = await props.params;
    const auth = await getAuthenticatedAppForUser();

    if (!auth || !auth.currentUser || !auth.currentUser.uid) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Verify the user has permission to manage integrations
    const hasPermission = await db.workspaceMembership.findFirst({
      where: {
        userId: auth.currentUser.uid,
        workspaceId: params.workspaceId,
        role: {
          permissions: {
            some: {
              id: Permission.MANAGE_INTEGRATIONS,
            },
          },
        },
      },
    });

    if (!hasPermission) {
      return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    }

    // Find the integration
    const workspaceIntegration = await db.workspaceIntegration.findFirst({
      where: {
        id: params.integrationId,
        workspaceId: params.workspaceId,
      },
      include: {
        github: true,
      },
    });

    if (!workspaceIntegration) {
      return NextResponse.json(
        { message: 'The GitHub integration for this workspace does not exist' },
        { status: 404 }
      );
    }

    const otherWorkspaces = await db.$transaction(async (tx) => {
      // Delete the GitHub integration
      await tx.workspaceIntegration.delete({
        where: {
          id: workspaceIntegration.id,
        },
      });

      // Check if there are other workspaces using this same GitHub installation
      return await tx.workspaceIntegration.findMany({
        where: {
          channel: workspaceIntegration.channel,
          integrationIdOnChannel: workspaceIntegration.integrationIdOnChannel,
        },
        select: {
          workspace: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    });

    let executedOnChannel = false;
    if (otherWorkspaces.length === 0) {
      // TODO: review this to be more generic and easier to reuse for other integrations (maybe an interface that must be implemented by all channels?)
      if (workspaceIntegration.github) {
        try {
          const response = await getInstance().octokit.request('DELETE /app/installations/{installation_id}', {
            installation_id: workspaceIntegration.github.installationId,
          });

          if (response.status === 204) {
            executedOnChannel = true;
          } else {
            logger.warn('Non 204 status uninstalling installation on GitHub:', response.status, 'data:', response.data);
          }
        } catch (e) {
          logger.error('Error uninstalling installation on GitHub:', e);
        }
      }
    }

    return NextResponse.json({
      otherWorkspaces: otherWorkspaces.map((ws) => {
        return {
          id: ws.workspace.id,
          name: ws.workspace.name,
        };
      }),
      executedOnChannel,
    });
  } catch (error) {
    logger.error('Error uninstalling installation:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/workspace/{workspaceId}/integration/{integrationId}:
 *   patch:
 *     summary: Patch an installation of a workspace
 *     description: Updates an installation of a workspace with the provided fields
 *     security:
 *       - BearerAuth: []
 *     tags:
 *       - Integration
 *       - Workspace
 *     parameters:
 *       - name: workspaceId
 *         in: path
 *         required: true
 *         description: The workspace ID
 *         schema:
 *           type: string
 *       - name: integrationId
 *         in: path
 *         required: true
 *         description: The integration ID to update
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 $ref: '#/components/schemas/IntegrationStatus'
 *     responses:
 *       200:
 *         description: Installation was updated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 executedOnChannel:
 *                   description: Whether the action was also executed on the integration channel
 *                   type: boolean
 *                 otherWorkspaces:
 *                   description: List of other workspaces using this installation
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         $ref: '#/components/schemas/Workspace/properties/id'
 *                       name:
 *                         $ref: '#/components/schemas/Workspace/properties/name'
 *       400:
 *         description: Invalid integration channel, operation not supported, or invalid body/status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invalid body: status is missing"
 *       401:
 *         description: Unauthorized - user not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Unauthorized"
 *       403:
 *         description: Forbidden - user lacks MANAGE_INTEGRATIONS permission in the workspace
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Forbidden"
 *       404:
 *         description: Integration not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "The GitHub integration for this workspace does not exist"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ workspaceId: string; integrationId: string }> }
) {
  try {
    const jsonBody = await request.json();

    const status = jsonBody?.status as IntegrationStatus;
    if (!status || !Object.values(IntegrationStatus).includes(status)) {
      return NextResponse.json({ message: 'Invalid body: status is missing' }, { status: 400 });
    }

    const params = await props.params;
    const auth = await getAuthenticatedAppForUser();

    if (!auth || !auth.currentUser || !auth.currentUser.uid) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Verify the user has permission to manage integrations
    const hasPermission = await db.workspaceMembership.findFirst({
      where: {
        userId: auth.currentUser.uid,
        workspaceId: params.workspaceId,
        role: {
          permissions: {
            some: {
              id: Permission.MANAGE_INTEGRATIONS,
            },
          },
        },
      },
    });

    if (!hasPermission) {
      return NextResponse.json({ message: 'Forbidden' }, { status: 403 });
    }

    // Find the integration
    const workspaceIntegration = await db.workspaceIntegration.findFirst({
      where: {
        id: params.integrationId,
        workspaceId: params.workspaceId,
      },
      include: {
        github: true,
      },
    });

    if (!workspaceIntegration) {
      return NextResponse.json(
        { message: 'The GitHub integration for this workspace does not exist' },
        { status: 404 }
      );
    }

    if (workspaceIntegration.status === IntegrationStatus.Synchronizing) {
      return NextResponse.json({ message: 'The integration is still synchronizing' }, { status: 400 });
    }

    await db.workspaceIntegration.update({
      where: {
        id: workspaceIntegration.id,
      },
      data: {
        status: status,
      },
    });

    // Check if there are other workspaces using this same GitHub installation
    const otherWorkspaces = await db.workspaceIntegration.findMany({
      where: {
        NOT: {
          id: workspaceIntegration.id,
        },
        channel: workspaceIntegration.channel,
        integrationIdOnChannel: workspaceIntegration.integrationIdOnChannel,
      },
      select: {
        workspace: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    let executedOnChannel = false;
    if (otherWorkspaces.length === 0 || status === IntegrationStatus.Active) {
      if (workspaceIntegration.github) {
        try {
          const currentStatus = await getInstance().octokit.request('GET /app/installations/{installation_id}', {
            installation_id: workspaceIntegration.github.installationId,
          });

          if (
            (currentStatus.data.suspended_at !== null && status === IntegrationStatus.Active) ||
            (currentStatus.data.suspended_at === null && status === IntegrationStatus.Suspended)
          ) {
            const requestUrl =
              status === IntegrationStatus.Suspended
                ? 'PUT /app/installations/{installation_id}/suspended'
                : 'DELETE /app/installations/{installation_id}/suspended';

            const response = await getInstance().octokit.request(requestUrl, {
              installation_id: workspaceIntegration.github.installationId,
            });

            if (response.status === 204) {
              executedOnChannel = true;
            } else {
              logger.warn(
                'Non 204 status when executing the',
                status,
                ' action in the installation on GitHub:',
                response.status,
                'data:',
                response.data
              );
            }
          } else {
            executedOnChannel = true;
          }
        } catch (e) {
          logger.error('Error when executing the', status, ' action in the installation on GitHub:', e);
        }
      }
    }

    return NextResponse.json({
      otherWorkspaces: otherWorkspaces.map((ws) => {
        return {
          id: ws.workspace.id,
          name: ws.workspace.name,
        };
      }),
      executedOnChannel,
    });
  } catch (error) {
    logger.error('Error uninstalling installation:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
