enum GitRepositoryVisibility {
  Internal
  Private
  Public
}

enum GitRepositoryState {
  Active
  Archived
  Deleted
}

model GitRepository {
  id String @id @default(uuid())

  /// The id of this repository in the integration channel
  idOnChannel String

  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  workspaceId String

  integration   WorkspaceIntegration? @relation(fields: [integrationId], references: [id], onDelete: Cascade)
  integrationId String?

  channel IntegrationChannel

  name          String
  defaultBranch String

  visibility GitRepositoryVisibility

  state      GitRepositoryState
  createdAt  DateTime
  deletedAt  DateTime?
  archivedAt DateTime?
  updatedAt  DateTime           @default(now())

  createdBy   IntegrationProfile? @relation("createdRepository", fields: [createdById], references: [id], onDelete: SetNull)
  createdById String?

  deletedBy   IntegrationProfile? @relation("deletedRepository", fields: [deletedById], references: [id], onDelete: SetNull)
  deletedById String?

  archivedBy   IntegrationProfile? @relation("archivedRepository", fields: [archivedById], references: [id], onDelete: SetNull)
  archivedById String?

  commits      GitCommit[]
  pullRequests GitPullRequest[]
  tasks        Task[]

  @@unique(name: "unique", [workspaceId, idOnChannel, channel])
}

model GitCommit {
  id String @id @default(uuid())

  sha String

  repositoryId String
  repository   GitRepository @relation(fields: [repositoryId], references: [id], onDelete: Cascade)

  branch String
  url    String

  committedAt DateTime

  authorId String?
  author   IntegrationProfile? @relation("authoredCommits", fields: [authorId], references: [id], onDelete: SetNull)

  commiterId String?
  commiter   IntegrationProfile? @relation("commitedCommits", fields: [commiterId], references: [id], onDelete: SetNull)

  pusherId String?
  pusher   IntegrationProfile? @relation("pushedCommits", fields: [pusherId], references: [id], onDelete: SetNull)

  message String

  modifiedFiles String[]
  addedFiles    String[]
  deletedFiles  String[]

  @@unique(name: "unique", [sha, branch, repositoryId])
}

model GitPullRequest {
  id String @id @default(uuid())

  /// The id of this pull request in the integration channel
  idOnChannel String

  repositoryId String
  repository   GitRepository @relation(fields: [repositoryId], references: [id], onDelete: Cascade)

  author   IntegrationProfile @relation("createdPullRequests", fields: [authorId], references: [id], onDelete: Cascade)
  authorId String

  title       String
  description String?

  state GitPullRequestState
  url   String?

  /// The pull request number if exists
  number Int?

  createdAt DateTime
  mergedAt  DateTime?
  closedAt  DateTime?
  updatedAt DateTime  @default(now())

  closedById String?
  closedBy   IntegrationProfile? @relation("closedPullRequests", fields: [closedById], references: [id], onDelete: SetNull)

  isDraft Boolean

  addedLines   Int
  deletedLines Int

  baseRef String?
  headRef String?

  baseSha              String?
  headSha              String?

  @@unique(name: "unique", [idOnChannel, repositoryId])
}

enum GitPullRequestState {
  OPEN
  CLOSED
  MERGED
}
