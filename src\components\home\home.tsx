'use client';

import { Link } from '@/i18n/navigation';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Container from '@mui/material/Container';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { useTheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { ChartLineIcon } from '@phosphor-icons/react/dist/ssr/ChartLine';
import { GearIcon } from '@phosphor-icons/react/dist/ssr/Gear';
import { GitBranchIcon } from '@phosphor-icons/react/dist/ssr/GitBranch';
import { RocketIcon } from '@phosphor-icons/react/dist/ssr/Rocket';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { DynamicLogo } from '@/components/core/logo';
import { paths } from '@/paths';

export function Home(): React.JSX.Element {
  const t = useTranslations('home.onboarding');
  const theme = useTheme();

  const onboardingSteps = [
    {
      id: 'git-integration',
      title: t('steps.gitIntegration.title'),
      description: t('steps.gitIntegration.description'),
      icon: <GitBranchIcon fontSize='var(--icon-fontSize-xl)' />,
      href: paths.settings.integrations.index,
      color: theme.palette.primary.main,
    },
    {
      id: 'explore-insights',
      title: t('steps.exploreInsights.title'),
      description: t('steps.exploreInsights.description'),
      icon: <ChartLineIcon fontSize='var(--icon-fontSize-xl)' />,
      href: paths.insights.delivery,
      color: theme.palette.success.main,
    },
    {
      id: 'customize-settings',
      title: t('steps.customizeSettings.title'),
      description: t('steps.customizeSettings.description'),
      icon: <GearIcon fontSize='var(--icon-fontSize-xl)' />,
      href: paths.settings.preferences,
      color: theme.palette.info.main,
    },
  ];

  return (
    <Box sx={{ pt: 0, pb: 8 }}>
      <Container maxWidth='lg'>
        <Stack spacing={6}>
          {/* Welcome Header */}
          <Stack spacing={2} sx={{ textAlign: 'center' }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
              <DynamicLogo height={60} width={240} />
            </Box>
            <Typography variant='h3' component='h1'>
              {t('welcome.title')}
            </Typography>
            <Typography variant='h6' color='text.secondary' sx={{ maxWidth: '800px', mx: 'auto' }}>
              {t('welcome.subtitle')}
            </Typography>
          </Stack>

          {/* Main Card */}
          <Card>
            <CardHeader title={t('getStarted.title')} subheader={t('getStarted.subtitle')} sx={{ pb: 0 }} />
            <CardContent>
              <Grid container spacing={3} sx={{ mt: 2 }}>
                {onboardingSteps.map((step) => (
                  <Grid key={step.id} size={{ md: 4, xs: 12 }}>
                    <Card variant='outlined' sx={{ height: '100%' }}>
                      <CardContent>
                        <Stack spacing={2}>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              bgcolor: `${step.color}15`,
                              color: step.color,
                              borderRadius: '50%',
                              p: 2,
                              width: 64,
                              height: 64,
                              mb: 1,
                            }}
                          >
                            {step.icon}
                          </Box>
                          <Typography variant='h6'>{step.title}</Typography>
                          <Typography variant='body2' color='text.secondary' sx={{ flexGrow: 1 }}>
                            {step.description}
                          </Typography>
                          <Box sx={{ pt: 2 }}>
                            <Button
                              component={Link}
                              href={step.href}
                              endIcon={<ArrowRightIcon />}
                              variant='contained'
                              sx={{
                                bgcolor: step.color,
                                '&:hover': {
                                  bgcolor: `${step.color}CC`,
                                },
                              }}
                            >
                              {t('common.getStarted')}
                            </Button>
                          </Box>
                        </Stack>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>

          {/* Quick Start Guide */}
          <Card>
            <CardHeader title={t('quickStart.title')} subheader={t('quickStart.subtitle')} />
            <Divider />
            <CardContent>
              <Stack direction={{ xs: 'column', md: 'row' }} spacing={4} alignItems='center'>
                <Box sx={{ flex: 1, display: 'flex', justifyContent: 'center' }}>
                  <Box
                    component='img'
                    src='/assets/auth-widgets.png'
                    alt={t('quickStart.imageAlt')}
                    sx={{
                      maxWidth: '100%',
                      height: 'auto',
                      maxHeight: 300,
                    }}
                  />
                </Box>
                <Stack spacing={3} sx={{ flex: 1 }}>
                  <Typography variant='body1'>{t('quickStart.description')}</Typography>
                  <Button
                    component={Link}
                    href={paths.git.overview}
                    variant='contained'
                    color='primary'
                    endIcon={<RocketIcon />}
                    size='large'
                  >
                    {t('quickStart.button')}
                  </Button>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Container>
    </Box>
  );
}
