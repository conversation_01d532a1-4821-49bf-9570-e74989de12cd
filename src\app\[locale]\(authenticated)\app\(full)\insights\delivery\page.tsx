import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { DeliveryDeadlines } from '@/components/insights/delivery-deadlines';
import { config } from '@/config';

export const metadata = {
  title: `Delivery & Deadlines | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('insights');

  return (
    <Stack spacing={3}>
      <Typography variant='h4'>{t('categories.deliveryDeadlines')}</Typography>
      <Typography color='text.secondary' variant='body1'>
        Tracking sprint progress and identifying bottlenecks in the delivery pipeline.
      </Typography>

      <DeliveryDeadlines />
    </Stack>
  );
}
