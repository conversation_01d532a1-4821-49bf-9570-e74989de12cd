enum TaskType {
    Bug
    Feature
}

model Task {
    id String @id

    channel IntegrationChannel
    type    TaskType

    workspaceId String
    workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

    author   IntegrationProfile? @relation(fields: [authorId], references: [id], onDelete: SetNull)
    authorId String?

    createdAt DateTime
    closedAt  DateTime?

    gitRepositories GitRepository[] @ignore

    @@unique([workspaceId, id, channel])
}
