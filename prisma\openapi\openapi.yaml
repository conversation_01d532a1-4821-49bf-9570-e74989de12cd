openapi: 3.1.0
info:
  title: Prisma API
  description: ""
  version: 1.0.0
paths: {}
components:
  schemas:
    Role:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        workspaceId:
          type: string
        workspace:
          $ref: "#/components/schemas/Workspace"
        memberships:
          type: array
          items:
            $ref: "#/components/schemas/WorkspaceMembership"
        permissions:
          type: array
          items:
            $ref: "#/components/schemas/Permissions"
      required:
        - id
        - createdAt
        - updatedAt
        - memberships
        - permissions
    Permissions:
      type: object
      properties:
        id:
          type: string
        roles:
          type: array
          items:
            $ref: "#/components/schemas/Role"
      required:
        - id
        - roles
    Dashboard:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        layout:
          type: object
        isDefault:
          type: boolean
        isPublic:
          type: boolean
        workspaceId:
          type: string
        workspace:
          $ref: "#/components/schemas/Workspace"
        createdById:
          type: string
        createdBy:
          $ref: "#/components/schemas/User"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        widgets:
          type: array
          items:
            $ref: "#/components/schemas/DashboardWidget"
      required:
        - id
        - name
        - layout
        - isDefault
        - isPublic
        - workspaceId
        - workspace
        - createdById
        - createdBy
        - createdAt
        - updatedAt
        - widgets
    DashboardWidget:
      type: object
      properties:
        id:
          type: string
        type:
          $ref: "#/components/schemas/WidgetType"
        title:
          type: string
        config:
          type: object
        x:
          type: integer
          format: int32
        y:
          type: integer
          format: int32
        width:
          type: integer
          format: int32
        height:
          type: integer
          format: int32
        dashboardId:
          type: string
        dashboard:
          $ref: "#/components/schemas/Dashboard"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
      required:
        - id
        - type
        - config
        - x
        - y
        - width
        - height
        - dashboardId
        - dashboard
        - createdAt
        - updatedAt
    GitRepository:
      type: object
      properties:
        id:
          type: string
        idOnChannel:
          type: string
          description: The id of this repository in the integration channel
        workspace:
          $ref: "#/components/schemas/Workspace"
        workspaceId:
          type: string
        integration:
          $ref: "#/components/schemas/WorkspaceIntegration"
        integrationId:
          type: string
        channel:
          $ref: "#/components/schemas/IntegrationChannel"
        name:
          type: string
        defaultBranch:
          type: string
        visibility:
          $ref: "#/components/schemas/GitRepositoryVisibility"
        state:
          $ref: "#/components/schemas/GitRepositoryState"
        createdAt:
          type: string
          format: date-time
        deletedAt:
          type: string
          format: date-time
        archivedAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        createdBy:
          $ref: "#/components/schemas/IntegrationProfile"
        createdById:
          type: string
        deletedBy:
          $ref: "#/components/schemas/IntegrationProfile"
        deletedById:
          type: string
        archivedBy:
          $ref: "#/components/schemas/IntegrationProfile"
        archivedById:
          type: string
        commits:
          type: array
          items:
            $ref: "#/components/schemas/GitCommit"
        pullRequests:
          type: array
          items:
            $ref: "#/components/schemas/GitPullRequest"
        tasks:
          type: array
          items:
            $ref: "#/components/schemas/Task"
      required:
        - id
        - idOnChannel
        - workspace
        - workspaceId
        - channel
        - name
        - defaultBranch
        - visibility
        - state
        - createdAt
        - updatedAt
        - commits
        - pullRequests
        - tasks
    GitCommit:
      type: object
      properties:
        id:
          type: string
        sha:
          type: string
        repositoryId:
          type: string
        repository:
          $ref: "#/components/schemas/GitRepository"
        branch:
          type: string
        url:
          type: string
        committedAt:
          type: string
          format: date-time
        authorId:
          type: string
        author:
          $ref: "#/components/schemas/IntegrationProfile"
        commiterId:
          type: string
        commiter:
          $ref: "#/components/schemas/IntegrationProfile"
        pusherId:
          type: string
        pusher:
          $ref: "#/components/schemas/IntegrationProfile"
        message:
          type: string
        modifiedFiles:
          type: string
        addedFiles:
          type: string
        deletedFiles:
          type: string
      required:
        - id
        - sha
        - repositoryId
        - repository
        - branch
        - url
        - committedAt
        - message
        - modifiedFiles
        - addedFiles
        - deletedFiles
    GitPullRequest:
      type: object
      properties:
        id:
          type: string
        idOnChannel:
          type: string
          description: The id of this pull request in the integration channel
        repositoryId:
          type: string
        repository:
          $ref: "#/components/schemas/GitRepository"
        author:
          $ref: "#/components/schemas/IntegrationProfile"
        authorId:
          type: string
        title:
          type: string
        description:
          type: string
        state:
          $ref: "#/components/schemas/GitPullRequestState"
        url:
          type: string
        number:
          type: integer
          format: int32
          description: The pull request number if exists
        createdAt:
          type: string
          format: date-time
        mergedAt:
          type: string
          format: date-time
        closedAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        closedById:
          type: string
        closedBy:
          $ref: "#/components/schemas/IntegrationProfile"
        isDraft:
          type: boolean
        addedLines:
          type: integer
          format: int32
        deletedLines:
          type: integer
          format: int32
        baseRef:
          type: string
        headRef:
          type: string
        baseSha:
          type: string
        headSha:
          type: string
      required:
        - id
        - idOnChannel
        - repositoryId
        - repository
        - author
        - authorId
        - title
        - state
        - createdAt
        - updatedAt
        - isDraft
        - addedLines
        - deletedLines
    GitHubIntegration:
      type: object
      properties:
        id:
          type: string
        updatedAt:
          type: string
          format: date-time
        integration:
          $ref: "#/components/schemas/WorkspaceIntegration"
        integrationId:
          type: string
        installationId:
          type: integer
          format: int32
        accountId:
          type: integer
          format: int32
        accountLogin:
          type: string
        accountType:
          $ref: "#/components/schemas/GitHubAccountType"
      required:
        - id
        - updatedAt
        - integration
        - integrationId
        - installationId
        - accountId
        - accountLogin
        - accountType
    GitHubWebhook:
      type: object
      properties:
        receivedAt:
          type: string
          format: date-time
        hookId:
          type: string
          description: X-GitHub-Hook-ID
        event:
          type: string
          description: X-GitHub-Event
        delivery:
          type: string
          description: X-GitHub-Delivery (GUID)
        hookInstallationTargetType:
          type: string
          description: X-GitHub-Hook-Installation-Target-Type
        hookInstallationTargetId:
          type: string
          description: X-GitHub-Hook-Installation-Target-ID
        installationId:
          type: integer
          format: int32
          description: When available, this is the installation ID of the installation
            that created the webhook (installation.id)
        installationAccountId:
          type: integer
          format: int32
          description: When available, this is the account ID of the installation that
            created the webhook (installation.account.id)
        installationAccountLogin:
          type: string
          description: When available, this is the login of the account that created the
            webhook (installation.account.login)
        processed:
          type: boolean
          description: If the webhook was processed successfully
        data:
          type: object
          description: Webhook payload
      required:
        - receivedAt
        - event
        - delivery
        - processed
        - data
    WorkspaceIntegration:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        workspace:
          $ref: "#/components/schemas/Workspace"
        workspaceId:
          type: string
        channel:
          $ref: "#/components/schemas/IntegrationChannel"
        status:
          $ref: "#/components/schemas/IntegrationStatus"
        integrationIdOnChannel:
          type: string
        github:
          $ref: "#/components/schemas/GitHubIntegration"
      required:
        - id
        - createdAt
        - updatedAt
        - workspace
        - workspaceId
        - channel
        - status
        - integrationIdOnChannel
    IntegrationState:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
          format: date-time
        workspace:
          $ref: "#/components/schemas/Workspace"
        workspaceId:
          type: string
        channel:
          $ref: "#/components/schemas/IntegrationChannel"
      required:
        - id
        - createdAt
        - workspace
        - workspaceId
        - channel
    IntegrationProfile:
      type: object
      properties:
        id:
          type: string
        workspace:
          $ref: "#/components/schemas/Workspace"
        workspaceId:
          type: string
        channel:
          $ref: "#/components/schemas/IntegrationChannel"
        idOnChannel:
          type: string
          description: >-
            The id of this profile in the channel.

            May be null if there is not enough information to determine the
            profile.
        username:
          type: string
        name:
          type: string
        emails:
          type: string
        links:
          type: array
          items:
            $ref: "#/components/schemas/UserProfileLink"
      required:
        - id
        - workspace
        - workspaceId
        - channel
        - emails
        - links
    Task:
      type: object
      properties:
        id:
          type: string
        channel:
          $ref: "#/components/schemas/IntegrationChannel"
        type:
          $ref: "#/components/schemas/TaskType"
        workspaceId:
          type: string
        workspace:
          $ref: "#/components/schemas/Workspace"
        author:
          $ref: "#/components/schemas/IntegrationProfile"
        authorId:
          type: string
        createdAt:
          type: string
          format: date-time
        closedAt:
          type: string
          format: date-time
      required:
        - id
        - channel
        - type
        - workspaceId
        - workspace
        - createdAt
    Team:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        parentId:
          type: string
        parent:
          $ref: "#/components/schemas/Team"
        children:
          type: array
          items:
            $ref: "#/components/schemas/Team"
        workspaceId:
          type: string
        workspace:
          $ref: "#/components/schemas/Workspace"
        members:
          type: array
          items:
            $ref: "#/components/schemas/TeamMembership"
      required:
        - id
        - createdAt
        - updatedAt
        - children
        - members
    TeamMembership:
      type: object
      properties:
        createdAt:
          type: string
          format: date-time
        teamId:
          type: string
        team:
          $ref: "#/components/schemas/Team"
        userId:
          type: string
        user:
          $ref: "#/components/schemas/User"
      required:
        - createdAt
        - teamId
        - team
        - userId
        - user
    User:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        displayName:
          type: string
        avatar:
          type: string
        country:
          type: string
        timezone:
          type: string
        phone:
          type: string
        language:
          type: string
        theme:
          type: string
        onboarding:
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        teams:
          type: array
          items:
            $ref: "#/components/schemas/TeamMembership"
        workspaces:
          type: array
          items:
            $ref: "#/components/schemas/WorkspaceMembership"
        invitesPerformed:
          type: array
          items:
            $ref: "#/components/schemas/WorkspaceInvite"
        invites:
          type: array
          items:
            $ref: "#/components/schemas/WorkspaceInvite"
        createdDashboards:
          type: array
          items:
            $ref: "#/components/schemas/Dashboard"
        links:
          type: array
          items:
            $ref: "#/components/schemas/UserProfileLink"
          description: >-
            A single user may have multiple profiles on different channels.

            For example a single user on BMS Pulse may have two different GitHub
            accounts.

            It will always have at least one profile for the BMS channel.
      required:
        - id
        - language
        - theme
        - onboarding
        - createdAt
        - updatedAt
        - teams
        - workspaces
        - invitesPerformed
        - invites
        - createdDashboards
        - links
    UserProfileLink:
      type: object
      properties:
        user:
          $ref: "#/components/schemas/User"
        userId:
          type: string
        profile:
          $ref: "#/components/schemas/IntegrationProfile"
        profileId:
          type: string
        workspace:
          $ref: "#/components/schemas/Workspace"
        workspaceId:
          type: string
      required:
        - user
        - userId
        - profile
        - profileId
        - workspace
        - workspaceId
    Workspace:
      type: object
      properties:
        id:
          type: string
          description: The workspace auto-generated id
        name:
          type: string
          description: |-
            The workspace name.
            Multiple workspaces may have the same name.
        avatar:
          type: string
          description: The workspace avatar picture
        createdAt:
          type: string
          format: date-time
          description: Date when the workspace have been created
        updatedAt:
          type: string
          format: date-time
          description: Last modification of the workspace
        roles:
          type: array
          items:
            $ref: "#/components/schemas/Role"
        teams:
          type: array
          items:
            $ref: "#/components/schemas/Team"
        invites:
          type: array
          items:
            $ref: "#/components/schemas/WorkspaceInvite"
        members:
          type: array
          items:
            $ref: "#/components/schemas/WorkspaceMembership"
        integrationStates:
          type: array
          items:
            $ref: "#/components/schemas/IntegrationState"
        integrations:
          type: array
          items:
            $ref: "#/components/schemas/WorkspaceIntegration"
        dashboards:
          type: array
          items:
            $ref: "#/components/schemas/Dashboard"
      required:
        - id
        - createdAt
        - updatedAt
        - roles
        - teams
        - invites
        - members
        - integrationStates
        - integrations
        - dashboards
    WorkspaceMembership:
      type: object
      properties:
        userId:
          type: string
          description: The user id
        workspaceId:
          type: string
        roleId:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        role:
          $ref: "#/components/schemas/Role"
        user:
          $ref: "#/components/schemas/User"
        workspace:
          $ref: "#/components/schemas/Workspace"
      required:
        - userId
        - workspaceId
        - roleId
        - createdAt
        - updatedAt
        - role
        - user
        - workspace
    WorkspaceInvite:
      type: object
      properties:
        id:
          type: string
        email:
          type: string
        token:
          type: string
        status:
          $ref: "#/components/schemas/InviteStatus"
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        workspaceId:
          type: string
        invitedBy:
          type: string
        userId:
          type: string
        invitedByUser:
          $ref: "#/components/schemas/User"
        user:
          $ref: "#/components/schemas/User"
        workspace:
          $ref: "#/components/schemas/Workspace"
      required:
        - id
        - status
        - createdAt
        - updatedAt
    Permission:
      type: string
      enum:
        - DELETE_WORKSPACE
        - INVITE_MEMBER
        - UPDATE_WORKSPACE
        - MANAGE_INTEGRATIONS
    WidgetType:
      type: string
      enum:
        - METRIC_CARD
        - SPARKLINE_CHART
        - HEATMAP
        - TEXT_INSIGHT
        - COMMITS_CHART
        - PULL_REQUESTS_CHART
        - VELOCITY_CHART
        - COPILOT_METRICS
    GitRepositoryVisibility:
      type: string
      enum:
        - Internal
        - Private
        - Public
    GitRepositoryState:
      type: string
      enum:
        - Active
        - Archived
        - Deleted
    GitPullRequestState:
      type: string
      enum:
        - OPEN
        - CLOSED
        - MERGED
    GitHubAccountType:
      type: string
      enum:
        - Enterprise
        - Organization
        - User
    IntegrationStatus:
      type: string
      enum:
        - Active
        - Inactive
        - Suspended
        - Uninstalled
        - Synchronizing
        - PermissionsOutdated
    IntegrationChannel:
      type: string
      enum:
        - GitHub
        - GitLab
        - Bitbucket
        - AzureDevOps
        - Slack
        - Jira
        - Email
    TaskType:
      type: string
      enum:
        - Bug
        - Feature
    InviteStatus:
      type: string
      enum:
        - PENDING
        - ACCEPTED
        - REJECTED
  responses: {}
  parameters: {}
  examples: {}
  requestBodies: {}
  headers: {}
  securitySchemes: {}
  links: {}
  callbacks: {}
tags: []
servers: []
