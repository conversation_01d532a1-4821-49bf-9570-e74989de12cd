'use client';

import { useRouter } from '@/i18n/navigation';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import * as React from 'react';

import { useAuth } from '@/contexts/firebase-auth-context';
import { paths } from '@/paths';

export interface AuthenticatedUserGuardProps {
  children: React.ReactNode;
}

export function AuthenticatedUserGuard({ children }: AuthenticatedUserGuardProps): React.JSX.Element {
  const router = useRouter();
  const { user, loading, error } = useAuth();
  const [isChecking, setIsChecking] = React.useState<boolean>(true);

  const checkAuthentication = React.useCallback(async (): Promise<void> => {
    if (loading) {
      return;
    }

    if (error) {
      setIsChecking(false);
      return;
    }

    if (!user) {
      console.debug('[AuthenticatedUserGuard]: User is not logged in, redirecting to sign in');
      router.replace(paths.auth.signIn);
      return;
    }

    setIsChecking(false);
  }, [loading, error, user, router]);

  React.useEffect(() => {
    checkAuthentication().catch(() => {
      // noop
    });
  }, [user, loading, error, checkAuthentication]);

  if (loading || isChecking) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert color='error'>{error}</Alert>;
  }

  return <React.Fragment>{children}</React.Fragment>;
}

export default AuthenticatedUserGuard;
