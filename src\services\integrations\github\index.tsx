import {
  Integration,
  IntegrationHandler,
  IntegrationInstallProps,
  IntegrationUninstallProps,
} from '@/services/integrations/integration';

import { logger } from '@/lib/logger/default-logger';
import { IntegrationChannel, Prisma } from '@prisma/client';
import { GitHubConfig } from './github-config';
import { GitHubDoc } from './github-doc';

// GitHub integration data
const githubIntegration: Integration = {
  id: IntegrationChannel.GitHub,
  title: 'GitHub',
  darkLogo: true,
  status: 'available',
  capabilities: [
    'pullRequests',
    'codeReview',
    'commit',
    'repoInsights',
    'teamPerformance',
    'aiAnalytics',
    'cicdPipelines',
  ],
};

export type GitHubWorkspaceIntegration =
  | Prisma.WorkspaceIntegrationGetPayload<{
      include: { github: true };
    }>
  | undefined;

// GitHub integration handler
export const githubHandler: IntegrationHandler = {
  // Return the integration data
  getIntegration: () => {
    return githubIntegration;
  },

  // Handle installation
  onInstallAction: async (props: IntegrationInstallProps) => {
    logger.info('Installing GitHub integration');

    const state = await props.integrationApiService.createState(props.workspaceId, IntegrationChannel.GitHub);

    if (!state || !state.id) {
      throw new Error('Failed to create GitHub integration state');
    }

    // Redirect user to github installation page
    const githubAppName = process.env.NEXT_PUBLIC_GITHUB_APP_NAME;

    if (!githubAppName) {
      throw new Error('NEXT_PUBLIC_GITHUB_APP_NAME environment variable is not set');
    }

    const installationUrl = `https://github.com/apps/${githubAppName}/installations/new?state=${state.id}`;

    logger.info('Redirecting to GitHub installation URL', { url: installationUrl });

    // Redirect to GitHub installation page
    window.location.href = installationUrl;

    return true;
  },

  // Handle uninstallation
  onUninstallAction: async (_props: IntegrationUninstallProps) => {
    logger.info('Uninstalling GitHub integration');

    //try {
    // TODO: implement uninstall completely
    // Call the API to uninstall the integration
    //await props.integrationApiService.uninstallIntegration(props.workspaceId, IntegrationChannel.GitHub);

    // Update integration status
    //githubIntegration.status = 'available';

    return true;
    //} catch (error) {
    //  logger.error('Error uninstalling GitHub integration', error);
    //  throw new Error('Failed to uninstall GitHub integration');
    //}
  },
  // Use ConfigComponent instead of getConfigComponent
  ConfigComponent: GitHubConfig,

  // Use DocComponent instead of getDocumentationComponent
  DocComponent: GitHubDoc,

  // This integration doesn't have a custom install flow
  hasCustomInstallFlow: () => false,
};
