import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useTranslations } from 'next-intl';
import NextLink from 'next/link';
import * as React from 'react';

import { GuestUserGuard } from '@/components/auth/guest-user-guard';
import { DynamicLogo } from '@/components/core/logo';
import { config } from '@/config';
import { paths } from '@/paths';

export interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps): React.JSX.Element {
  const t = useTranslations('layout');

  return (
    <GuestUserGuard>
      <Box
        sx={{
          display: { xs: 'flex', lg: 'grid' },
          flexDirection: 'column',
          gridTemplateColumns: '1fr 1fr',
          minHeight: '100%',
        }}
      >
        <Box sx={{ display: 'flex', flex: '1 1 auto', flexDirection: 'column' }}>
          <Box sx={{ p: 3 }}>
            <Box component={NextLink} href={paths.landing} sx={{ display: 'inline-block', fontSize: 0 }}>
              <DynamicLogo height={32} width={122} />
            </Box>
          </Box>
          <Box
            sx={{
              alignItems: 'center',
              display: 'flex',
              flex: '1 1 auto',
              justifyContent: 'center',
              p: 3,
            }}
          >
            <Box sx={{ maxWidth: '450px', width: '100%' }}>{children}</Box>
          </Box>
        </Box>
        <Box
          sx={{
            alignItems: 'center',
            background: 'radial-gradient(50% 50% at 50% 50%, #122647 0%, #090E23 100%)',
            color: 'var(--mui-palette-common-white)',
            display: { xs: 'none', lg: 'flex' },
            justifyContent: 'center',
            p: 3,
          }}
        >
          <Stack spacing={3}>
            <Stack spacing={1}>
              <Typography
                color='inherit'
                sx={{ fontSize: '24px', lineHeight: '32px', textAlign: 'center' }}
                variant='h1'
              >
                {t('welcome')}{' '}
                <Box component='span' sx={{ color: '#15b79e' }}>
                  {config.site.name}
                </Box>
              </Typography>
              <Typography align='center' variant='subtitle1'>
                {t('site.description')}
              </Typography>
            </Stack>
            <Box sx={{ display: 'flex', justifyContent: 'center' }}>
              <Box
                component='img'
                alt='Widgets'
                src='/assets/auth-widgets.png'
                sx={{ height: 'auto', width: '100%', maxWidth: '600px' }}
              />
            </Box>
          </Stack>
        </Box>
      </Box>
    </GuestUserGuard>
  );
}
