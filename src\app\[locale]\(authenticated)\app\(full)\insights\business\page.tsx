import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { BusinessValue } from '@/components/insights/business-value';
import { config } from '@/config';

export const metadata = {
  title: `Business Value | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('insights');

  return (
    <Stack spacing={3}>
      <Typography variant='h4'>{t('categories.businessValue')}</Typography>
      <Typography color='text.secondary' variant='body1'>
        Measuring business impact and optimizing investment allocation for maximum ROI.
      </Typography>

      <BusinessValue />
    </Stack>
  );
}
