import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Stack from '@mui/material/Stack';
import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import * as React from 'react';

import { PreferencesForm } from '@/components/settings/preferences-form';
import { config } from '@/config';

export const metadata = {
  title: `Preferences | Settings | ${config.site.name}`,
} satisfies Metadata;

export default async function Page(): Promise<React.JSX.Element> {
  const t = await getTranslations('settings');

  return (
    <Stack spacing={3}>
      <Card>
        <CardHeader subheader={t('preferences.subheader')} title={t('preferences.title')} />
        <Divider />
        <CardContent>
          <PreferencesForm />
        </CardContent>
      </Card>
    </Stack>
  );
}
