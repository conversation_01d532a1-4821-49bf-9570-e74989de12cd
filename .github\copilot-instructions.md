# Base Instruction

- When commenting code, use clear and concise comments that explain the purpose of the code, especially for complex logic. Example: "This function calculates X."
- Do not add comments to explain the changes you made, as the commit history will already reflect those changes. Example: "added this because of X" is not necessary.
- Comments should not be added to explain what is already clear from the code itself. For example, avoid comments like "import X library"

# Structure

- We have the backend and frontend in a single Next.js application, with API routes implemented within Next.js. We use App Router and server components, ensuring a clear separation between client and server components.
- Always check for compile errors and runtime errors before proceeding with implementation.

# Technology Stack

- Firebase for authentication, with SSR approach from Firebase documentation.
- TypeScript is preferred over JavaScript for all implementations.
- We are using Prisma as our ORM for database interactions and also for type definitions for both frontend and backend.
  - Prisma schema models are located in #file:../prisma/models

## Code Style and Formatting

- Use TypeScript for all new files.
- To check for format issues, run `npm run style:fix`

## Tests

- All new features must have unit tests.
- When testing, create basic test functionality first, run with `npm run test:coverage`, check if they are working, and then implement the rest of the tests
- After fixing any issues with test files, you must run `npm run test:coverage` before proceeding to create more tests
- Check an existing test file to use as a reference. Make sure to use the reference the same type of test you are performing (frontend vs backend tests).
